@extends('common.layout')
@section('title', 'Dashboard | Client Portal')
<link rel="stylesheet" href="{{asset('unit-portal/css/style.css')}}">
<link rel="stylesheet" href="{{asset('css/dashboard-filters.css')}}">
@section('content')
<!-- Content Wrapper. Contains page content -->
@canany(['client_portal_dashboard_financial_information','unit_portal_dashboard_financial_information'])
<div class="Container_Fluid_Main">

    <div class="row headTitleBtm">
        <div class="col-sm-12">
            <div class="GpBoxHeading no-bdr pb0">
                <h4>
                    Dashboard
                </h4>
            </div>
        </div>
    </div>

    <div class="MainBoxRow mb25">
        <!-- Filter Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="unit-filter" class="form-label">Select Unit</label>
                                    <select id="unit-filter" name="unit_id" class="form-control">
                                        <option value="">All</option>
                                        @foreach($units as $unit)
                                        <option value="{{ $unit->clientUnitId }}"
                                            {{ $selectedUnitId == $unit->clientUnitId ? 'selected' : '' }}>
                                            {{ $unit->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="financial-year-filter" class="form-label">Financial Year</label>
                                    <select id="financial-year-filter" name="financial_year" class="form-control"
                                        {{ !$selectedUnitId || ($selectedUnitId && count($financialYears) == 0) ? 'disabled' : '' }}>
                                        <option value="">Select Financial Year</option>
                                        @if($selectedUnitId && count($financialYears) > 0)
                                        @foreach($financialYears as $year)
                                        <option value="{{ $year }}"
                                            {{ $selectedFinancialYear == $year ? 'selected' : '' }}>
                                            @php
                                            $yearValue = is_numeric($year) ? (int)$year : date('Y');
                                            $nextYear = ($yearValue + 1) % 100;
                                            @endphp
                                            {{ $yearValue }}-{{ str_pad($nextYear, 2, '0', STR_PAD_LEFT) }}
                                        </option>
                                        @endforeach
                                        @endif
                                    </select>
                                    @if(!$selectedUnitId)
                                        <small class="text-muted">Select a specific unit to make this filter active</small>
                                    @elseif($selectedUnitId && count($financialYears) == 0)
                                        <small class="text-muted">No financial years available for this unit</small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="month-filter" class="form-label">Month</label>
                                    <select id="month-filter" name="month" class="form-control" 
                                        {{ !$selectedUnitId || !$selectedFinancialYear || ($selectedUnitId && count($financialYears) == 0) ? 'disabled' : '' }}>
                                        <option value="">Select Month</option>
                                        @if($selectedUnitId && $selectedFinancialYear && isset($months) && count($months) > 0)
                                            @foreach($months as $month)
                                                <option value="{{ $month['value'] }}"
                                                    {{ isset($selectedMonth) && $selectedMonth == $month['value'] ? 'selected' : '' }}>
                                                    {{ $month['label'] }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @if(!$selectedUnitId || !$selectedFinancialYear || ($selectedUnitId && count($financialYears) == 0))
                                        <small class="text-muted">
                                            @if(!$selectedUnitId)
                                                Select a specific unit to make this filter active
                                            @elseif($selectedUnitId && count($financialYears) == 0)
                                                No financial years available for this unit
                                            @elseif(!$selectedFinancialYear)
                                                Select a financial year to make this filter active
                                            @endif
                                        </small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="week-filter" class="form-label">Select Week</label>
                                    <select id="week-filter" name="week" class="form-control" 
                                        {{ !$selectedUnitId || !$selectedFinancialYear || ($selectedUnitId && count($financialYears) == 0) ? 'disabled' : '' }}>
                                        <option value="">Select Week</option>
                                        @if($selectedUnitId && $selectedFinancialYear && isset($weeks) && count($weeks) > 0)
                                            @foreach($weeks as $week)
                                                <option value="{{ $week['week_number'] }}"
                                                    {{ isset($selectedWeek) && $selectedWeek == $week['week_number'] ? 'selected' : '' }}>
                                                    {{ $week['label'] }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @if(!$selectedUnitId || !$selectedFinancialYear || ($selectedUnitId && count($financialYears) == 0))
                                        <small class="text-muted">
                                            @if(!$selectedUnitId)
                                                Select a specific unit to make this filter active
                                            @elseif($selectedUnitId && count($financialYears) == 0)
                                                No financial years available for this unit
                                            @elseif(!$selectedFinancialYear)
                                                Select a financial year to make this filter active
                                            @endif
                                        </small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-1 text-right" style="padding-left: 0;">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                              
                                        <button type="button" id="apply-filters-btn" class="btn btn-filter" 
                                                title="Apply Filters">
                                            <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
                                        </button>
                                        <button type="button" id="reset-filters-btn" class="btn btn-reset" 
                                                title="Reset All Filters">
                                            <img src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
                                        </button>
                                 
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cards Section -->
    <div>
        @include('common.dashboard.component.card.header_cards')
    </div>

    <!-- Graph Section -->
    <div class="row">
        <div class="col-md-6">
            @yield('budjet')
        </div>
        <div class="col-md-6">
            @yield('expense')
        </div>
    </div>

    <!-- Unit Expense Overview -->
    <!-- <div>
        @yield('expense_overview')
    </div> -->

    <!-- Reason for Booking -->
    <div>
        @yield('booking_cards')
    </div>

    <!-- <div>
        @yield('budjet_expense')
    </div> -->


    @hasSection('graph1')
    <div class="row mt-3">
        <div class="col-12">
            @yield('graph1')
        </div>
    </div>
    @endif

   
</div>
 @endcanany
</section>
</div>


<!-- Custom Styles for Dashboard -->
@yield('dashboard-styles')

<!-- Dashboard Filters JavaScript -->
<script src="{{ asset('js/dashboard-filters.js') }}"></script>

<!-- Custom Scripts for Dashboard -->
@yield('dashboard-scripts')
@endsection


