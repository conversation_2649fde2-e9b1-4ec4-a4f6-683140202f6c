@extends('common.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('unit-portal/css/managementReports.css') }}">
    <link rel="stylesheet" href="{{ asset('unit-portal/css/daterangepicker.css') }}">
    <style>
        table {
            background: white;
            border: 1px solid grey;
            border-collapse: collapse;
        }

        table thead th,
        table tfoot th {
            background: rgba(0, 0, 0, .1);
        }

        table caption {
            padding: .5em;
        }

        table th,
        table td {
            padding: .5em;
            border: 1px solid lightgrey;
        }

        /* Highlighted row style for selected week */
     .highlighted-week-row {
    background-color: #fff3cd !important;
    outline: 2px solid #21ff00 !important; /* Green outline */
    box-shadow: 0 0 10px rgba(3, 3, 3, 0.5);
}

        .highlighted-week-row td {
            background-color: #9c9797 !important;
            font-weight: bold;
        }
    </style>
@endsection
@section('content')


    <div class="Container_Fluid_Main">
        <div class="row headTitleBtm">
            <div class="col-sm-12">
                <div class="GpBoxHeading no-bdr pb0">
                    <h4>
                        Timesheet Approval
                    </h4>
                </div>
            </div>
        </div>
    </div>


    <div class="Container_Fluid_Main">

        <div class="mngsScrlVie">
            <div class="graphPage" fetch="{{ route('management.reports.graph') }}" token="{{ csrf_token() }}">
            </div>


            <div class="MainBoxRow mb25">
                @if (\Illuminate\Support\Facades\Auth::user()->portal_type == 'App\Models\Client')
                    <form method="GET" action="{{ route('ts.approvals') }}">
                        <div class='col-sm-4'>
                            <div class='form-group'>
                                <select class="form-control select2" id="filter_unit" name="filter_unit">
                                    <option value="0">Units</option>
                                    @foreach ($units as $unit)
                                        <option @if (request('filter_unit') == $unit->clientUnitId) selected @endif
                                            value="{{ $unit->clientUnitId }}">{{ $unit->alias }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class='col-sm-3'>
                            <div class='form-group'>
                                <select class="form-control select2" id="filter_tax_year" name="filter_tax_year">
                                    <option value="0">Tax Year</option>
                                    @foreach ($TaxYear as $year)
                                        <option
                                            @if (request('filter_tax_year') == $year->taxYearId) selected @elseif($year->default == 1) selected @endif
                                            value="{{ $year->taxYearId }}">{{ $year->taxYearFrom }}-{{ $year->taxYearTo }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class='col-sm-3'>
                            <div class='form-group'>
                                <select class="form-control select2" id="filter_tax_week" name="filter_tax_week">
                                    <option value="0">Week No</option>
                                    @for ($i = 1; $i <= 52; $i++)
                                        <option @if (request('filter_tax_week') == $i) selected @endif
                                            value="{{ $i }}">{{ $i }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                        </div>

                        {{-- Preserve action_week if present --}}
                        @if (request()->has('action_week'))
                            <input type="hidden" name="action_week" value="{{ request('action_week') }}">
                        @endif
                        <button type="submit" class="btn-filter">
                            <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
                        </button>
                        <a href="{{ route('clear.filter') }}">
                            <button type="button" class="btn-reset">
                                <img src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
                            </button>
                        </a>
                    </form>
                @endif

            </div>

            <div style="margin-top: 0;" class="shift_box mb10">
                <div class="shift_bottom">

                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Week No</th>
                                <th>Weekend</th>
                                <th>Unit</th>
                                <th>No of shifts current week</th>
                                <th>No of shifts from Previous week</th>
                                <th>Total No of Ts to approve</th>
                                <th>No of Ts Approved</th>
                                <th>No of shifts moved to next week</th>
                                <th>Status</th>
                                @canany(['client_portal_timesheet_for_approval_edit_time_sheet',
                                    'unit_portal_timesheet_for_approval_edit_time_sheet'])
                                    <th style="text-align: right;">Action</th>
                                @endcanany
                            </tr>
                        </thead>
                        <tbody>
                            @php
    // Sort so Action == 1 comes first
                                   $sortedSummary = collect($summaryData)->sortByDesc('Action');
                            @endphp
                            @forelse ($sortedSummary as $row)
                                <tr style="background-color: #f7f7f7;" 
                                    class="week-summary-row" 
                                    data-week-number="{{ $row['Week No'] }}" 
                                    data-unit-id="{{ $row['unitId'] }}"
                                    data-weekending="{{ $row['Weekending'] }}">
                                    <td>{{ $row['Week No'] }}</td>
                                    <td>{{ \Carbon\Carbon::parse(request('weekend'))->format('d-m-y') ?? '-' }}</td>
                                    <td>{{ $row['Unit'] }}</td>
                                    <td>{{ $row['No of shifts current week'] }}</td>
                                    <td>{{ $row['No of shifts from Previous week'] }}</td>
                                    <td>{{ $row['Total No of Ts to approve'] }}</td>
                                    <td>{{ $row['No of Ts Approved'] }}</td>
                                    <td>{{ $row['No of shifts moved to next week'] }}</td>
                                    <td>@if($row['Total No of Ts to approve']==0) Approved @else Waiting @endif</td>
                                    @canany(['client_portal_timesheet_for_approval_edit_time_sheet',
                                        'unit_portal_timesheet_for_approval_edit_time_sheet'])
                                        <td style="text-align: right;">
                                            <form method="GET" action="{{ route('ts.approvals') }}">
                                                <input type="hidden" value="{{ $row['Week No'] }}" name="action_week">
                                                <input type="hidden" name="enic" value="{{ $row['Enic'] }}">
                                                <input type="hidden" name="weekend" value="{{ $row['Weekending'] }}">
                                                <input type="hidden" name="ts_to_approve" id="ts_to_approve"
                                                    value="{{ $row['Total No of Ts to approve'] }}">

                                                {{-- Preserve action_week if present --}}
                                                @if (empty(request('action_week')))
                                                    <input type="hidden" value="{{ $row['Week No'] }}" name="action_week">
                                                @else
                                                    <input type="hidden" value="{{ $row['Week No'] }}" name="action_week">
                                                @endif
                                                {{-- Preserve unit filter --}}

                                                <input type="hidden" name="filter_unit" value="{{ $row['unitId'] }}">

                                                @if (request()->has('filter_tax_year'))
                                                    <input type="hidden" name="filter_tax_year"
                                                        value="{{ request('filter_tax_year') }}">
                                                @endif
                                                @if (request()->has('filter_tax_week'))
                                                    <input type="hidden" name="filter_tax_week"
                                                        value="{{ request('filter_tax_week') }}">
                                                @endif

                                                {{-- @if (request()->has('searchStaff'))
                                                    <input type="hidden" name="searchStaff"
                                                        value="{{ request('searchStaff') }}">
                                                @endif

                                                @if (request()->has('searchCategory'))
                                                    <input type="hidden" name="searchCategory"
                                                        value="{{ request('searchCategory') }}">
                                                @endif

                                                @if (request()->has('searchShift'))
                                                    <input type="hidden" name="searchShift"
                                                        value="{{ request('searchShift') }}">
                                                @endif --}}

                                                <button type="submit" class="btn-three-sm week-action-btn" 
                                                        data-week="{{ $row['Week No'] }}" 
                                                        data-unit="{{ $row['unitId'] }}"
                                                        data-weekending="{{ $row['Weekending'] }}"
                                                        id="remove-action_week">
                                                    <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                </button>
                                            </form>

                                        </td>
                                    @endcanany
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="{{ auth()->user()->can('client_portal_timesheet_for_approval_edit_time_sheet') || auth()->user()->can('unit_portal_timesheet_for_approval_edit_time_sheet') ? 9 : 8 }}"
                                        class="text-center">No data available</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                    @if ($selectedWeekTimesheets && $selectedWeekTimesheets->count())
                        <h4 class="mt25 mb15">Detailed Timesheets for Week {{ $actionWeek }}</h4>

                        <div class="MainBoxRow">
                            <div class="box">
                                <div class="box-body">
                                    <form action="{{ route('ts.approvals') }}" method="GET"
                                        enctype="multipart/form-data" id="timesheet_form">
                                        @if (request()->has('filter_unit'))
                                            <input type="hidden" name="filter_unit"
                                                value="{{ request('filter_unit') }}">
                                        @endif

                                        @if (request()->has('action_week'))
                                            <input type="hidden" name="action_week"
                                                value="{{ request('action_week') }}">
                                        @endif


                                        <div class="row">
                                            <div class='col-sm-2'>
                                                <div class='form-group'>
                                                    <label for="date"> Category</label>
                                                    <select class="form-control select2" id="searchCategory"
                                                        name="searchCategory">
                                                        <option value=""></option>
                                                        @foreach ($categories as $category)
                                                            <option @if (request('searchCategory') == $category->categoryId) selected @endif
                                                                value="{{ $category->categoryId }}">{{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>

                                            <div class='col-sm-2'>
                                                <div class='form-group'>
                                                    <label for="date">Shift</label>
                                                    <select class="form-control select2" id="searchShift"
                                                        name="searchShift">
                                                        <option value=""></option>
                                                        @foreach ($shifts as $shift)
                                                            <option @if (request('searchShift') == $shift->shiftId) selected @endif
                                                                value="{{ $shift->shiftId }}">{{ $shift->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <p class="error">Shift is required</p>
                                                </div>
                                            </div>
                                            <div class='col-sm-2'>
                                                <div class='form-group'>
                                                    <label for="date">Staff</label>
                                                    <select class="form-control select2" id="searchStaff"
                                                        name="searchStaff">
                                                        <option value=""></option>
                                                        @foreach ($staffs as $staff)
                                                            <option @if (request('searchStaff') == $staff->staffId) selected @endif
                                                                value="{{ $staff->staffId }}">
                                                                {{ $staff->forname . ' ' . $staff->surname }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <p class="error">Staff is required</p>
                                                </div>
                                            </div>
                                            @if (request()->has('filter_unit'))
                                                <input type="hidden" name="filter_unit"
                                                    value="{{ request('filter_unit') }}">
                                            @endif
                                            @if (request()->has('filter_tax_year'))
                                                <input type="hidden" name="filter_tax_year"
                                                    value="{{ request('filter_tax_year') }}">
                                            @endif
                                            @if (request()->has('filter_tax_week'))
                                                <input type="hidden" name="filter_tax_week"
                                                    value="{{ request('filter_tax_week') }}">
                                            @endif
                                             @if (request()->has('weekend'))
                                                <input type="hidden" name="weekend"
                                                    value="{{ request('weekend') }}">
                                            @endif
                                            <input type="hidden" value="{{ request('weekend') }}" id="weekenddate">

                                            <div class='col-sm-2 m-t-25'>
                                                <button class="btn btn-filter filterNow" data-action="">
                                                    <img src="{{ asset('unit-portal/images/filter.svg') }}"
                                                        alt="Filter">
                                                </button>
                                                <a href="{{ route('clear.filter') }}">
                                                    <button type="button" class="btn btn-reset">
                                                        <img src="{{ asset('unit-portal/images/reset.svg') }}"
                                                            alt=“Reset”>
                                                    </button>
                                                </a>
                                            </div>

                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="ActBtn action-buttons mt-3">
                            @canany(['client_portal_timesheet_for_approval_approve_time_sheet',
                                'unit_portal_timesheet_for_approval_approve_time_sheet'])
                                <button type="button" class="tmsBtn" id="approveSelected"
                                    @if (request('ts_to_approve') == 0) disabled @endif>Approve
                                    Selected
                                </button>
                            @endcanany
                            @canany(['client_portal_timesheet_for_approval_submit_for_invoice',
                                'unit_portal_timesheet_for_approval_submit_for_invoice'])
                                <button type="button" class="tmsBtn  @if (request('ts_to_approve') == 0) tmsBtnActive @endif"
                                    @if (request('ts_to_approve') != 0) disabled @endif id="generateInvoiceSelected">
                                    Submit for
                                    Invoice
                                </button>
                            @endcanany
                            @canany(['client_portal_timesheet_for_approval_move_to_next_week',
                                'unit_portal_timesheet_for_approval_move_to_next_week'])
                                <button type="button" class="tmsBtn" id="moveToNextWeek"
                                    @if (request('ts_to_approve') == 0) disabled title="Not able to move" @endif>Move to Next
                                    Week
                                </button>
                            @endcanany
                            @canany(['client_portal_timesheet_for_approval_export',
                                'unit_portal_timesheet_for_approval_export'])
                                <button type="button" class="tmsBtn downloadBtn tmsBtnActive" id="exportExcel">Export to
                                    Excel
                                </button>
                            @endcanany
                        </div>

                        <table class="table table-bordered" id="my-table">

                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"> Booking ID</th>
                                    <th>Weekend</th>
                                    <th>TS Number</th>
                                    <th>Week</th>
                                    <th>Date</th>
                                    <th>Account</th>
                                    <th>Shift Type</th>
                                    <th>Unit</th>
                                    <th>Staff</th>
                                    <th>Log</th>
                                    <th>Start</th>
                                    <th>End</th>
                                    <th>Hours</th>
                                    <th>Hourly Rate</th>
                                    @if (request('enic') == 1)
                                    <th>ENIC</th>
                                    @endif
                                    <th>TA</th>
                                    <th>Additional Pay</th>
                                    <th>Line Total</th>
                                    <th style="text-align: right">Action</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($selectedWeekTimesheets as $ts)
                                    <tr @if ($ts->verification_from_portal == 1) style="background-color: #dedede !important; " @endif
                                        class="@if ($ts->verification_from_portal == 1) ApprovedClass  @else NotApprovedClass @endif">
                                        <td>
                                            <input type="checkbox" class="form-check-input @if ($ts->verification_from_portal == 1) ApprovedCheckbox  @else NonApprovedCheckbox @endif" name="ts_ids[]"
                                                value="{{ $ts->timesheetId }}" data-weekend="{{ request('weekend') }}" >
                                            {{ $ts->bookingId }}
                                        </td>
                                        <td>{{ \Carbon\Carbon::parse(request('weekend'))->format('d-m-y') ?? '-' }}</td>
                                        <td>{{ $ts->number ?? '-' }}</td>
                                        <td>{{ $ts->week ?? '-' }}</td>
                                        <td>{{ \Carbon\Carbon::parse($ts->date)->format('d-m-y') ?? '-' }}</td>
                                        <td>{{ $ts->category->account_code }}{{ $ts->shift->account_code }}</td>
                                        <td>{{ $ts->category->name ?? '-' }}-{{ $ts->shift->name ?? '-' }}</td>
                                        <td>{{ $ts->unit->name ?? '-' }}</td>
                                        <td>{{ $ts->staff->full_name ?? '-' }}</td>
                                        <td> <a class="{{ $ts->chats->where('color', 1)->count() > 0 ? 'log_button warngBtn' : 'log_button' }}"
                                                booking_id="{{ $ts->bookingId }}" unit_id="{{ $ts->unitId }}"
                                                staff="{{ $ts->staff->full_name }}" unit="{{ $ts->unit->alias }}"
                                                category="{{ $ts->category->name }}" shift="{{ $ts->shift->name }}"
                                                date="{{ date('d-M-Y, D', strtotime($ts->date)) }}"
                                                start="{{ date('h:i', strtotime($ts->start_time)) }}"
                                                end="{{ date('h:i', strtotime($ts->end_time)) }}">
                                                <i class="fa fa-clock-o" aria-hidden="true"></i>
                                            </a></td>
                                        <td>{{ date('H:i', strtotime($ts->startTime)) }}</td>
                                        <td>{{ date('H:i', strtotime($ts->endTime)) }}</td>
                                        <td>{{ number_format($ts->unitHours, 2) }}</td>
                                        <td @if ($ts->hourly_rate == null) onclick="location.reload();" title="Click to update" @endif>
                                        £{{ $ts->hourly_rate }}</td>
                                            @if (request('enic') == 1)
                                            <td>-</td>
                                        @endif
                                        <td>£{{ number_format(0, 2) }}</td>
                                        <td>£{{ number_format($ts->unit_expense_amount, 2) }}</td>
                                        <td>£{{ number_format($ts->hourly_rate * $ts->unitHours + $ts->unit_expense_amount, 2) }}
                                        </td>
                                        <td style="text-align: right">
                                            @canany(['client_portal_timesheet_for_approval_approve_time_sheet',
                                                'unit_portal_timesheet_for_approval_approve_time_sheet'])
                                                <form method="POST" style="display:inline-block;">
                                                    @csrf
                                                    <button class="btn btn-sm btn-three-sm approveForm"
                                                        @if ($ts->verification_from_portal == 1) disabled title="Approved"  @else title="Approve" @endif
                                                        fetch="{{ route('ts.approve', ['id' => $ts->timesheetId]) }}"
                                                        type="button" id="">
                                                        @if ($ts->verification_from_portal == 1)
                                                            <i class="fa fa-thumbs-up" aria-hidden="true"></i>
                                                        @else
                                                            <i class="fa fa-check" aria-hidden="true"></i>
                                                        @endif
                                                    </button>
                                                </form>
                                            @endcanany
                                            @canany(['client_portal_timesheet_for_approval_verification',
                                                'unit_portal_timesheet_for_approval_verification'])
                                                <form method="POST" style="display:inline-block;">
                                                    @csrf
                                                    <button class="btn btn-sm btn-three-sm declineForm" type="button"
                                                        data-toggle="modal" data-target="#declineReasonModal"
                                                        @if ($ts->verification_from_portal == 1 || $ts->verification_from_portal == 2) disabled data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Need Verification" @else title="Need Verification" @endif
                                                        fetch="{{ route('ts.decline', ['id' => $ts->timesheetId]) }}">
                                                        @if ($ts->verification_from_portal == 2)
                                                            <i class="fa fa-minus-circle" aria-hidden="true"></i>
                                                        @else
                                                            <i class="fa fa-times" aria-hidden="true"></i>
                                                        @endif
                                                    </button>
                                                </form>
                                            @endcanany
                                            <button type="button" class="btn btn-sm btn-three-sm openTimesheetModal"
                                                title="View"
                                                data-m_shifttype="{{ $ts->shift->name }} - {{ $ts->category->name }}"
                                                data-m_bookingid="{{ $ts->bookingId }}"
                                                data-m_unit="{{ $ts->unit->name }}"
                                                data-m_staff="{{ $ts->staff->full_name }}"
                                                data-m_date="{{ $ts->date }}" data-m_week="{{ $ts->week }}"
                                                data-m_linetotal="£{{ round($ts->hourly_rate * $ts->unitHours + $ts->unit_expense_amount, 2) }}"
                                                data-toggle="modal" data-target="#popTimesheet"
                                                data-timesheet-id="{{ $ts->timesheetId }}"
                                                data-start-time="{{ date('H:i', strtotime($ts->startTime)) }}"
                                                data-end-time="{{ date('H:i', strtotime($ts->endTime)) }}"
                                                data-total-hours="{{ number_format($ts->unitHours, 2) }}"
                                                data-image-url="{{ 'https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/timesheets/' . $ts->timesheet->image ?? '' }}"
                                                data-aaproval="{{ $ts->verification_from_portal }}"
                                                approved=" @if ($ts->verification_from_portal == 1) Approved @else Approve @endif"
                                                declined=" @if ($ts->verification_from_portal == 1) Declined @else Decline @endif"
                                                data-account="{{ $ts->category->account_code }}{{ $ts->shift->account_code }}">
                                                <i class="fa fa-eye" aria-hidden="true"></i>
                                            </button>

                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <table class="table table-bordered" id="my-table">

                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"> Booking ID</th>
                                    <th>Weekend</th>
                                    <th>TS Number</th>
                                    <th>Week</th>
                                    <th>Date</th>
                                    <th>Account</th>
                                    <th>Shift Type</th>
                                    <th>Unit</th>
                                    <th>Staff</th>
                                    <th>Log</th>
                                    <th>Start</th>
                                    <th>End</th>
                                    <th>Hours</th>
                                    <th>Hourly Rate</th>
                                    @if (request('enic') == 1)
                                        <th>ENIC</th>
                                    @endif
                                    <th>TA</th>
                                    <th>Additional Pay</th>
                                    <th>Line Total</th>
                                    <th style="text-align: right">Action</th>
                                </tr>
                            </thead>
                            <tbody>


                                <tr>
                                    <td colspan="100"> No data available</td>
                                </tr>

                            </tbody>
                        </table>
                    @endif
                    <!-- Pagination -->

                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="popTimesheet" role="dialog">
        <div class="modal-dialog" role="document" style="width:1000px;">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="timesheetModalLabel">Timesheet Details</h5>
                    <button style="position: absolute;top: 12px;right: 0;" type="button" class="close text-white"
                        data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row timeunitpop">
                        <div class="col-md-12">
                            <h3><span id="m_unit"></span></h3>
                        </div>
                        <div class="col-md-12">
                            <p> <strong><span id="m_shifttype"></span></strong></p> <span class="TimeUnSpan">|</span>
                            <p> <strong><span id="m_bookingid"></span></strong></p> <span class="TimeUnSpan">|</span>
                            <p> <strong><span id="m_staff"></span></strong></p> <span class="TimeUnSpan">|</span>
                            <p>Account Code : <strong> <span id="m_account"></span></strong></p> <span
                                class="TimeUnSpan">|</span>
                            <p>Date : <strong><span id="m_date"></span></strong></p> <span class="TimeUnSpan">|</span>
                            <p>Week :<strong> <span id="m_week"></span></strong></p>
                        </div>

                        <div class="col-md-12 TimehiPara">
                            <p>Start Time : <strong><span id="modalStartTime"></span></strong></p> <span
                                class="TimeUnSpan">|</span>
                            <p>End Time :<strong><span id="modalEndTime"></span></strong></p> <span
                                class="TimeUnSpan">|</span>
                            <p>Total Hours :<strong> <span id="modalTotalHours"></span></strong></p> <span
                                class="TimeUnSpan">|</span>
                            <p>Line Total : <strong><span id="m_linetotal"></span></strong></p>
                        </div>
                        <div class="col-md-12">
                            <img id="modalTimesheetImage" src="" alt="Timesheet Image"
                                class="img-fluid img-responsive" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <form method="POST" style="display:inline-block;">
                        @csrf
                        <button type="submit" class="btn btn-primary approveForm approvemodalbutton"><i
                                class="fa fa-check" aria-hidden="true"></i></button>
                    </form>
                    <form method="POST" style="display:inline-block;">
                        @csrf
                        <button type="button" class="btn btn-two openDeclineModal declinemodalbutton"
                            data-toggle="modal" data-target="#declineReasonModal"><i class="fa fa-times"
                                aria-hidden="true"></i></button>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <!-- Decline Reason Modal -->
    <div class="modal fade" id="declineReasonModal" tabindex="-1" role="dialog" aria-labelledby="declineModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="declineModalLabel">Need clarity?</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="declineReason">Please enter the details:</label>
                            <textarea class="form-control" name="decline_reason" id="declineReason" rows="4" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger declineFormSubmit">Submit</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @include('unitPortal.bookingUnitLogBook')
@endsection

@section('scripts')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="{{ asset('unit-portal/js/daterangepicker.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.14.1/xlsx.full.min.js"></script>
    <script>
        $('.downloadBtn').on('click', function() {
            // Clone the table to avoid affecting the original table
            var $originalTable = $('#my-table');
            var $clonedTable = $originalTable.clone();

            // Remove the "Action" column (index 11)
            $clonedTable.find('tr').each(function() {
                $(this).find('th:eq(11), td:eq(11)').remove();
            });

            // Export the cleaned table
            var wb = XLSX.utils.table_to_book($clonedTable[0], {
                sheet: "Sheet name"
            });
            var wbout = XLSX.write(wb, {
                bookType: 'xlsx',
                bookSST: true,
                type: 'binary'
            });

            function s2ab(s) {
                var buf = new ArrayBuffer(s.length);
                var view = new Uint8Array(buf);
                for (var i = 0; i < s.length; i++) {
                    view[i] = s.charCodeAt(i) & 0xFF;
                }
                return buf;
            }

            saveAs(new Blob([s2ab(wbout)], {
                type: "application/octet-stream"
            }), 'Timesheets.xlsx');
        });

        $(document).ready(function() {
            
           
            $('input[name="date"]').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    format: 'DD-MM-YYYY',
                    cancelLabel: 'Clear'
                }
            });
            $('input[name="date"]').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format(
                    'DD-MM-YYYY'));
            });
            $('input[name="date"]').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
            $(document).on('click', '.openTimesheetModal', function() {
                const $btn = $(this);
                const timesheetId = $btn.data('timesheet-id');
                const approve = $btn.data('approved');
                const decline = $btn.data('declined');
                const startTime = $btn.data('start-time');
                const endTime = $btn.data('end-time');
                const account = $btn.data('account');
                const totalHours = $btn.data('total-hours');
                const imageUrl = $btn.data('image-url');
                const m_unit = $btn.data('m_unit');
                const m_shifttype = $btn.data('m_shifttype');
                const m_bookingId = $btn.data('m_bookingid');
                const m_staff = $btn.data('m_staff');
                const m_date = $btn.data('m_date');
                const m_week = $btn.data('m_week');
                const m_linetotal = $btn.data('m_linetotal');
                const apprval = $btn.data('aaproval');

                $('#m_linetotal').text(m_linetotal);
                $('#m_week').text(m_week);
                $('#m_date').text(m_date);
                $('#m_staff').text(m_staff);
                $('#m_bookingid').text(m_bookingId);
                $('#m_shifttype').text(m_shifttype);
                $('#m_unit').text(m_unit);
                $('#m_account').text(account);

                $('#modalStartTime').text(startTime);
                $('#modalEndTime').text(endTime);
                $('#modalTotalHours').text(totalHours);
                $('#modalTimesheetImage').attr('src', imageUrl);

                $('.approveForm').attr('fetch', '/approve/' + timesheetId);
                $('.declineFormSubmit').attr('fetch', '/decline/' + timesheetId);
                $('.approveForm').text(approve);
                $('.declineFormSubmit').text(decline);

                $('#timesheetModalPop').modal('show');
                if (apprval == 1) {
                    $('.approvemodalbutton').attr('disabled', 'disabled');
                    $('.declinemodalbutton').attr('disabled', 'disabled');
                    return false;
                } else if (apprval == 2) {
                    $('.approvemodalbutton').removeAttr('disabled');
                    $('.declinemodalbutton').attr('disabled', 'disabled');
                    return false;
                } else {
                    $('.approvemodalbutton').removeAttr('disabled');
                    $('.declinemodalbutton').removeAttr('disabled');
                }
            });
            // Approve AJAX
            $('.approveForm').on('click', function(e) {
                e.preventDefault();
                let url = $(this).attr('fetch');
                 if ($('.NotApprovedClass').length ==
                    1) { //The count will be zero the selected is the count 1
                    var newValue = 0;
                } else if ($('.NotApprovedClass').length == 0) {
                    var newValue = 0;
                } else if ($('.NotApprovedClass').length > 1) {
                    var newValue = Number($('.NotApprovedClass').length) - 1;
                }
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        alert(response.message);
                        $('#timesheetModalPop').modal('hide');
                        let url = new URL(window.location.href);
                        url.searchParams.set('ts_to_approve', newValue);
                        history.replaceState(null, '', url.toString());
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('An error occurred while approving.');
                    }
                });
            });

            // Decline AJAX
            $('.declineForm').on('click', function(e) {
                let url = $(this).attr('fetch');
                $('.declineFormSubmit').attr('fetch', url);
            });
            $('.declineFormSubmit').on('click', function(e) {
                e.preventDefault();
                let url = $(this).attr('fetch');
                let reason = $('#declineReason').val();

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        decline_reason: reason
                    },
                    success: function(response) {
                        alert(response.message);
                        $('#declineReasonModal').modal('hide');
                        location.reload(); // Or remove the row dynamically
                    },
                    error: function(xhr) {
                        alert('An error occurred while declining.');
                    }
                });
            });
            //selct all checkboxes

            // Select/Deselect All Checkboxes
           $('#selectAll').on('change', function() {
                    $('input[name="ts_ids[]"]').prop('checked', this.checked);

                    let selected = $('input[name="ts_ids[]"]:checked');

                    // Check if any selected has the class ApprovedCheckbox
                    let hasApproved = selected.filter('.ApprovedCheckbox').length > 0;
                    if(selected.length > 0){
                    if (hasApproved) {
                        $('#approveSelected,#moveToNextWeek')
                            .attr('disabled', true)
                            .removeClass('tmsBtnActive');
                    } else {
                        $('#approveSelected,#moveToNextWeek')
                            .removeAttr('disabled')
                            .addClass('tmsBtnActive');
                    }
                  }
            });


            // Uncheck Select All if any individual checkbox is unchecked
          $(document).on('change', 'input[name="ts_ids[]"]', function() {
                // Handle select all checkbox state
                if (!this.checked) {
                    $('#selectAll').prop('checked', false);
                } else if ($('input[name="ts_ids[]"]:not(:checked)').length === 0) {
                    $('#selectAll').prop('checked', true);
                }

                // Check if ANY selected checkbox has ApprovedCheckbox class
                let hasApproved = $('input[name="ts_ids[]"]:checked').filter('.ApprovedCheckbox').length > 0;
                if($('input[name="ts_ids[]"]:checked').length > 0){
                    if (hasApproved) {
                        $('#approveSelected,#moveToNextWeek').attr('disabled', true)
                                                            .removeClass('tmsBtnActive');
                    } else {
                        $('#approveSelected,#moveToNextWeek').removeAttr('disabled')
                                                            .addClass('tmsBtnActive');
                    }
                }
           });


            // Approve Selected
            $('#approveSelected').on('click', function() {

                let selected = [];
                $('input[name="ts_ids[]"]:checked').each(function() {
                    selected.push($(this).val());
                });
                if (selected.length === 0) {
                    alert('Please select at least one timesheet.');
                    return;
                }
                if (!confirm('Are you sure you want to approve the selected timesheets?')) return;
                if ($('.NotApprovedClass').length ==
                    1) { //The count will be zero the selected is the count 1
                    var newValue = 0;
                } else if ($('.NotApprovedClass').length == 0) {
                    var newValue = 0;
                } else if ($('.NotApprovedClass').length > 1) {
                    var newValue = Number($('.NotApprovedClass').length) - 1;
                }

                $.ajax({
                    url: '/approve-multiple',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        timesheet_ids: selected
                    },
                    success: function(response) {
                        alert('timesheets approved successfully.');
                        let url = new URL(window.location.href);
                        url.searchParams.set('ts_to_approve', newValue);
                        history.replaceState(null, '', url.toString());
                        location.reload();
                    },
                    error: function() {
                        alert('Error while approving selected timesheets.');
                    }
                });
            });
            $('#remove-action_week').on('click', function() {
                const url = new URL(window.location.href);
                url.searchParams.delete('action_week'); // 👈 Remove the 'status' param
                // window.history.replaceState({}, '', url); // Update the URL without reload
                //return false;
                location.reload();
            });
            // Move to next week Selected
            $('#moveToNextWeek').on('click', function() {
                let selected = [];
                let weekend = '';
                $('input[name="ts_ids[]"]:checked').each(function() {
                    selected.push($(this).val());
                });
                if (selected.length === 0) {
                    alert('Please select at least one timesheet.');
                    return;
                }
                $('input[name="ts_ids[]"]:checked').each(function() {
                    weekend = $(this).data('weekend'); // The data-weekend attribute
                    //console.log("Weekend:", weekend);
                });
                if (!confirm('Are you sure you want to move the selected timesheets?')) return;

                $.ajax({
                    url: '/move-to-next-week',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        timesheet_ids: selected,weekend:weekend
                    },
                    success: function(response) {
                        alert('Information moved to next week successfully.');
                        location.reload();
                    },
                    error: function() {
                        alert('Error while approving selected timesheets.');
                    }
                });
            });


            //Generate Invoice for Selected
            $('#generateInvoiceSelected').on('click', function() {
                let selected = [];
                $('input[name="ts_ids[]"]:checked').each(function() {
                    selected.push($(this).val());
                });
                if (selected.length === 0) {
                    alert('Please select at least one timesheet.');
                    return;
                }
                let weekend = $('#weekenddate').val();

                if (!confirm('Are you sure you want to generate the selected timesheets?')) return;

                $.ajax({
                    url: 'generate-invoice-multiple',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        timesheet_ids: selected,
                        weekenddate: weekend
                    },
                    success: function(response) {
                        alert(
                            'Thank You. Your Invoices has been Generated. It’ll be reflected shortly in the invoices Section.');
                        location.reload();
                    },
                    error: function() {
                        alert('Error while approving selected timesheets.');
                    }
                });
            });

            $(function() {
                $('#searchDate').daterangepicker({
                    autoUpdateInput: false, // Prevent automatic update
                    locale: {
                        format: 'DD-MM-YYYY',
                        cancelLabel: 'Clear'
                    },
                    showDropdowns: true,
                    opens: 'left',
                });

                // Set value when a date is selected
                $('#searchDate').on('apply.daterangepicker', function(ev, picker) {
                    $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate
                        .format('DD-MM-YYYY'));
                });

                // Clear value when 'Clear' is clicked
                $('#searchDate').on('cancel.daterangepicker', function(ev, picker) {
                    $(this).val('');
                });
            });
            //Log Section
            $('.log_button').click(function() {
                var dataUrl = $("#BookingUnitLogBook").attr('get-url');
                var headHtml = `<span class=""> ` + $(this).attr('unit') + `</span> <span class=""> ` + $(
                        this).attr('category') + `</span>  <span class=""> ` + $(this).attr('staff') +
                    `</span> | <span class=""> ` + $(this).attr('date') + `</span> | <span class=""> ` + $(
                        this).attr('shift') + `: ` + $(this).attr('start') + ` - ` + $(this).attr('end') +
                    ` </span> `;
                $('.chat_contents').html('');
                $('#BookingUnitLogBook .TopHeadTxt').html(headHtml);

                var token = $("#BookingUnitLogBook").attr('token');
                var booking_id = $(this).attr('booking_id');
                $('#unit_id').val($(this).attr('unit_id'));
                $('#unit_name').val($(this).attr('unit'));
                $('#booking_id').val($(this).attr('booking_id'));
                $('#BookingUnitLogBook').show();
                if (booking_id != "") {
                    $.ajax({
                        type: 'POST',
                        url: dataUrl,
                        data: {
                            _token: token,
                            booking_id: booking_id,
                        },
                        success: function(response) {
                            var datas = response.data;
                            var chat_list = ``;

                            for (var i = 0; i < datas.length; i++) {
                                // Inline style check for color value "one"
                                var inlineStyle = '';
                                if (datas[i].color == 1) {
                                    inlineStyle =
                                    'background-color: #f2f28a !important;'; // __Set the inline style directly (changed line)__
                                }
                                if (datas[i].type == 1) {
                                    chat_list += `<li class="tl-item" ng-repeat="item in retailer_history" style="${inlineStyle}">
                                      <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                                      </div>
                                      <div class="item-title  colorchange_chat"> <span class="LuftTitle"> ` + datas[i]
                                        .author_name +
                                        `(<b>${datas[i].user?.role?.name}</b>) : </span> ` +
                                        datas[i].content + `</div>
                                      <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> ` +
                                        datas[i].date_human + `</div>
                                    </li>`;
                                } else {
                                    chat_list += `<li class="tl-item" ng-repeat="item in retailer_history" style="${inlineStyle}">
                                  <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                                  </div>
                                  <div class="item-title"><span class="NsgOrange"> Nurses Group : </span> ` + datas[i]
                                        .content + `</div>
                                  <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> ` + datas[
                                            i].date_human + `</div>
                                </li>`;
                                }
                            }
                            $('.chat_contents').html(chat_list);
                        }
                    });
                }



            });
            //Log section ends

        });
    </script>
    <script>
        $(document).ready(function() {

            // ✅ Toggle "activeBtn" class based on checkbox selection
            function toggleApproveButtonClass() {
            
                const selected = $('input[name="ts_ids[]"]:checked').length;
                
            }

            // ✅ Select/Deselect All Checkboxes
            $('#selectAll').on('change', function() {
                const isChecked = this.checked;
                $('input[name="ts_ids[]"]').prop('checked', isChecked);
                const selected = $('input[name="ts_ids[]"]:checked').length;
              
                toggleApproveButtonClass();
            });

            // ✅ Individual checkbox toggle
            $(document).on('change', 'input[name="ts_ids[]"]', function() {
                const allChecked = $('input[name="ts_ids[]"]:not(:checked)').length === 0;
                $('#selectAll').prop('checked', allChecked);
                toggleApproveButtonClass();
            });
            $('.BookingUnitLogBook').click(function(e) {
                $("#BookingUnitLogBook").hide();
                return false;
            });
            $(document).on('submit', '#chat_form', function(e) {
                e.preventDefault();
                var dataUrl = $(this).attr('fetch');
                var token = $(this).attr('token');
                var content = $("#content").val();
                if (content == '') {
                    return false;
                }
                var author_id = $("#author_id").val();
                var booking_id = $("#booking_id").val();
                var unit_id = $("#unit_id").val();
                var unitName = $("#BookingUnitLogBook").attr('unit-name');
                var html = `<li class="tl-item" ng-repeat="item in retailer_history">
                  <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                  </div>
                  <div class="item-title"> <span class="LuftTitle"> ` + unitName + `: </span> ` + content + ` </div>
                  <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> Just Now </div>
                </li>`;
                $('#BookingUnitLogBook ul').prepend(html);
                $('#BookingUnitLogBook #content').val('');
                $.ajax({
                    type: "POST",
                    url: dataUrl,
                    data: {
                        '_token': token,
                        'unit_id': unit_id,
                        'booking_id': booking_id,
                        'author': author_id,
                        'content': content
                    },
                    success: function(data) {
                        // Refresh the page after successful chat save
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    },

                });

            });
        });
    </script>
    <script>
        $(document).ready(function() {
            // Function to highlight the selected week row
            function highlightSelectedWeekRow() {
                const actionWeek = '{{ $actionWeek ?? "" }}';
                const filterUnit = '{{ request("filter_unit") ?? "" }}';
                const weekend = '{{ request("weekend") ?? "" }}';
                
                // Remove any existing highlights first
                $('.week-summary-row').removeClass('highlighted-week-row');
                
                if (actionWeek) {
                    // Build selector to find the matching row
                    let targetRow = null;
                    
                    $('.week-summary-row').each(function() {
                        const rowWeek = $(this).data('week-number');
                        const rowUnit = $(this).data('unit-id');
                        const rowWeekending = $(this).data('weekending');
                        
                        // Match by week number
                        if (rowWeek == actionWeek) {
                            // If unit filter is applied, also match unit
                            if (filterUnit && filterUnit !== '0') {
                                if (rowUnit == filterUnit) {
                                    targetRow = $(this);
                                    return false; // break loop
                                }
                            } else {
                                // If weekend is specified, use it for more precise matching
                                if (weekend && rowWeekending === weekend) {
                                    targetRow = $(this);
                                    return false; // break loop
                                } else if (!weekend) {
                                    // If no weekend specified, take first match
                                    targetRow = $(this);
                                    return false; // break loop
                                }
                            }
                        }
                    });
                    
                    if (targetRow && targetRow.length) {
                        // Add highlight class
                        targetRow.addClass('highlighted-week-row');
                        
                        // Smooth scroll to the highlighted row
                        $('html, body').animate({
                            scrollTop: targetRow.offset().top - 100
                        }, 500);
                    }
                }
            }
            
            // Highlight row on page load if detailed timesheets are shown
            @if($selectedWeekTimesheets && $selectedWeekTimesheets->count() > 0)
                setTimeout(function() {
                    highlightSelectedWeekRow();
                }, 100);
            @endif
            
            // Handle action button clicks to add immediate visual feedback
            $(document).on('click', '.week-action-btn', function(e) {
                $('#searchStaff, #searchShift, #searchCategory').val();
                removeMultipleParams(['searchStaff', 'searchShift', 'searchCategory']);
                
                // Remove highlights from all rows first
                $('.week-summary-row').removeClass('highlighted-week-row');
                
                // Highlight the clicked row immediately
                const clickedRow = $(this).closest('.week-summary-row');
                clickedRow.addClass('highlighted-week-row');
                
                // Store the clicked row data for after page reload
                const weekNumber = clickedRow.data('week-number');
                const unitId = clickedRow.data('unit-id');
                const weekending = clickedRow.data('weekending');
                
                // Add loading state to button
                const originalHtml = $(this).html();
                $(this).html('<i class="fa fa-spinner fa-spin" aria-hidden="true"></i>');
                
                // Restore button after a delay if needed
                setTimeout(() => {
                    $(this).html(originalHtml);
                }, 3000);
            });
            // Function to remove a specific URL parameter
            function removeMultipleParams(params) {
                let url = new URL(window.location.href);
                params.forEach(p => url.searchParams.delete(p));
                window.history.replaceState({}, document.title, url.pathname + url.search);
        }
            // Remove highlight when clearing filters or removing action_week
            $(document).on('click', '#remove-action_week', function() {
                $('.week-summary-row').removeClass('highlighted-week-row');
                
                const url = new URL(window.location.href);
                url.searchParams.delete('action_week');
                window.location.href = url.toString();
            });
            
            // Also handle clear filter links
            $(document).on('click', '.btn-reset, a[href*="clear.filter"]', function() {
                $('.week-summary-row').removeClass('highlighted-week-row');
            });
            
            // Re-highlight when filters change and action_week is present
            $(document).on('click', '.btn-filter', function() {
                if ('{{ $actionWeek ?? "" }}') {
                    setTimeout(function() {
                        highlightSelectedWeekRow();
                    }, 500);
                }
            });
        });
    </script>
@endsection