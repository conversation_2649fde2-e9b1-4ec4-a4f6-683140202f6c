<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        {{ \Illuminate\Support\Facades\Auth::user()->portal_type == 'App\Models\Client'?" Client Portal" : ' Unit Portal' }}
    </title>
    <link rel="icon" type="image/png" href="{{asset('unit-portal/images/favicon.png')}}" />
    <link rel="stylesheet" href="{{asset('unit-portal/css/reset.css')}}">
    <link rel="stylesheet" href="{{asset('css/font-awesome/css/font-awesome.min.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/jquery.custom-scrollbar.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/jquery-clockpicker.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/modal.css')}}">
    <link rel="stylesheet" href="{{asset('css/jquery-ui.css')}}">
    <link rel="stylesheet" href="{{asset('client-portal/css/select2.min.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/responsive.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/sweetalert2.min.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/style.css')}}">
    <link rel="stylesheet" href="{{asset('unitArea/assets/css/coreadmin.css')}}?v={{time()}}">
    <link rel="stylesheet" href="{{asset('unitArea/assets/css/amend.css')}}">
    <link rel="stylesheet" href="{{asset('unit-portal/css/client.css')}}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @yield('styles')
</head>

<body>
    <!-- <div id="global-loader" style="display: none;" class=""></div> -->
    @include('common.leftmenu')
    <div id="vertical-scrollbar-demo" class="gray-skin scroller_sk right_section">
        @include('common.headermenu')

        @yield('content')

        {{--

            


      <div class="add_booking" cost-fetch="#" token="{{csrf_token()}}" categories="#" date="{{date('d-m-Y')}}"
        shifts="#" contacts="#">
        <div class="addBooksDivs">
            <div class="full_width">
                <div class="fby_one w10Percntge">
                    <p>Date</p>
                    <input type="text" name="date[]" class="txt_bx datepicker" value="{{date('d-m-Y')}}"
                        readonly="readonly">
                </div>
                <div class="fby_one w10Percntge">
                    <p>Shift</p>
                    <select name="shift[]" class="txt_bx shiftSel">
                        <option value=""></option>
                        <option value="">name</option>
                    </select>
                </div>
                <div class="fby_one w10Percntge">
                    <p>Staff Category</p>
                    <select name="category[]" class="txt_bx categorySwitch">

                        <option value=""></option>
                        <option value="">category name</option>

                    </select>
                </div>
                <div class="sby_one">
                    <p>Numbers</p>
                    <select name="numbers[]" class="txt_bx">
                        @for($i=1;$i< 11;$i++) <option value="{{$i}}">{{$i}}</option>
                            @endfor
                    </select>
                </div>
                <div class="fby_one w10Percntge">
                    <p>Start</p>
                    <input type="text" name="start_time[]" class="txt_bx startTime clockpicker">
                </div>
                <div class="fby_one w10Percntge">
                    <p>End</p>
                    <input type="text" name="end_time[]" class="txt_bx endTime clockpicker">
                </div>

                <div class="fby_one" style="width: 13%;">
                    <p>Requested By</p>
                    <input type="text" value="" name="requestedBy[]" class="txt_bx" readonly />
                </div>
                <div class="sby_one">
                    <p>Reason</p>
                    <select name="reasonBooking[]" class="txt_bx">
                        <option value="1">Staff Sickness</option>
                        <option value="2">Holiday cover</option>
                        <option value="3">Vacant Position</option>
                        <option value="4">New Resident admission</option>
                        <option value="5">1 to 1 care</option>
                        <option value="6">Extra staff requirement</option>
                        <option value="7">Staff training day</option>
                    </select>
                </div>
                <div class="fby_one">
                    <p>Additional Notes(if)</p>
                    <input type="text" name="impNotes[]" class="txt_bx">
                </div>
            </div>
        </div>
        <div class="right">
            <input type="button" class="add_newrow" value="Add Another Shift" manager="">
            <input type="button" url="#" token="{{csrf_token()}}" class="add_save" value="Save">
        </div>
    </div>
    <div class="blue_strip">Daily Shift Request Summary - Ordered by Date and Time of the Request Placed</div>
    <div class="scrolViewnsr">

        <div class="shift_box">
            <div class="shift_top">
                <p>
                <h5>18-May-2024</h5> - Details of <span>10 Shifts</span> booked on this date as follows</p>
            </div>
            <div class="shift_bottom">
                <div class="shift_line">
                    <div class="bk_id bold">Booking ID</div>
                    <div class="bk_date bold">Date</div>
                    <div class="bk_shift bold">Shift</div>
                    <div class="bk_cagry bold">Category</div>
                    <div class="bk_staff bold">Staff</div>
                    <div class="bk_staff exploreBtn bold">Explore</div>
                    <div class="bk_cagry bold">Profile</div>
                    <div class="bk_amnd bold" style="width: 8%;">Amend</div>
                    <div class="bk_status bold" style="width: 8% !important;">Status</div>
                    <div class="bk_details bold">Details</div>
                </div>
                <ul>
                    <li>
                        <div class="bk_id p25px">50</div>
                        <div class="bk_date p25px">18-May-2024 Sat</div>
                        <div class="bk_shift p25px">Early</div>
                        <div class="bk_cagry p25px">RGN</div>
                        <div class="bk_staff ">
                            Searching Now
                        </div>
                        <div class="bk_cagry ">
                            <a href="javascript:void(0)" class="exploreBtn profile"><i class="fa fa-location-arrow"
                                    aria-hidden="true"></i></a>
                        </div>
                        <div class="bk_cagry"><a href='javascript:void(0)' class='profile'>Searching</a></div>
                        <div class="bk_amnd" style="width: 8% !important;">
                            <button bookid="7" token="" fetch="http://localhost:8080/unit-portal/get-booking"
                                class="view amendNow" timeDiff="">
                                <i class="fa fa-wrench" aria-hidden="true"></i>
                            </button>

                        </div>
                        <div class="bk_status" style="width: 8% !important;">
                            <a href="#" class="confrimd">Confirmed</a>

                        </div>

                        <div class="bk_details">
                            <div class='font10'>Matthew Dennis-Andrews | Phone</div>
                            <div class='font10'><span class='redClr'>Notice Given - <strong>12 Hrs</span></strong></div>
                            <div class='font10'>Reason - No Reason</div>
                            <div class='font10'>18-05-2024 14:22</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        --}}
    </div>
    <div class="modal" id="unableToCancelModal">
       <div class="modal-dialog" style="width: 1010px;">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header header_txt">
                    <h4 class="modal-title"><b>Booking Amend</b> <button type="button" class="close"
                            data-dismiss="modal">×</button> </h4>
                </div>
                <div class="row" style=" margin: 0; ">
                    <div class="col-sm-12 ptl-20 bookDataHtml">
                    </div>

                    <div class="col-sm-12 ptl-20">
                        <div class="amd_fullwidth">
                            <h4 class="amdOK">The shift starts in less than 24 hours, Please contact our office, <span
                                    class="hil_cl">01935 315031</span> to make any changes.</h4>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="text-center  m-t-10 amendActionButtons">
                            <button type="button" class="amdbtn_green amendClose">OK</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="modal_new_requestby" class="modal">
        <!-- Modal content -->
        <div class="modal_changepass">
            <div class="modal-header">
                <span class="close closeBtnModal">&times;</span>
                <h2>Add New Contact</h2>
            </div>
            <div class="modal-body">
                <div class="inlineBlock">
                    <p>Full Name</p>
                    <input type="text" name="NewCnt_name" class="txt_bx_modal">
                </div>
                <div class="inlineBlock">
                    <p>Position</p>
                    <input type="text" name="NewCnt_position" class="txt_bx_modal">
                </div>
                <div class="inlineBlock">
                    <p>Phone</p>
                    <input type="text" name="NewCnt_phone" class="txt_bx_modal">
                </div>
                <div class="inlineBlock">
                    <p>Email</p>
                    <input type="text" name="NewCnt_email" class="txt_bx_modal">
                </div>
            </div>
            <div class="modal-footer">
                <input type="button" class="add_save_modal amdbtn_green" value="Save" action="#"
                    token="{{csrf_token()}}">
                <input type="button" token="" class="amdbtn_red add_save_modal_close closeBtnModal" value="Close">
            </div>
        </div>
    </div>

    <div class="FooterBottm">
        <span class="footCopy1"> &copy; {{date('Y')}} Smartseek.co.uk </span>
        <span class="footCopy2"> Version 1.0.0 </span>
        <span class="footCopy3"> Phone: +44 1935 315031 </span>
    </div>

    <section id="fixed-form-container" class="chatNotifcnt">
        <div class="button"><i class="fa fa-envelope" aria-hidden="true"></i></div>
        <div class="body hidden">
            <h2 class="inntxt"></h2>
            <p class="luftbkID">Booking ID: <span></span></p>
            <p class="luftbkID unitName">Unit: <span></span></p>
            <p class="luftbkID dateItem">Date: <span></span></p>
            <p class="luftbkID staffNamedge">Staff: <span></span></p>
            <p class="luftp"></p>
        </div>
    </section>
    {{-- <audio id="newChat" src="{{asset('sounds/')}}" preload="auto"></audio> --}}
</body>
<style>
<style>.bk_date {
    width: 12% !important;
}

.bk_shift {
    width: 7% !important;
}

.bk_cagry {
    width: 8% !important;
}

.bk_staff {
    width: 16% !important;
}

.bk_status {
    width: 13% !important;
}

.bk_details {
    width: 15% !important;
}

.select2-container--default .select2-selection--single {
    height: 36px;
    padding-top: 2px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #494949 !important;
    font-size: 13px;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 12px !important;
}

.select2-results__option {
    font-size: 12px !important;
}
</style>

<script src="{{asset('client-portal/js/moment.min.js')}}"></script>
<script src="{{asset('client-portal/js/jquery-ui.min.js')}}"></script>
<script src="{{asset('unit-portal/js/jquery.custom-scrollbar.js')}}"></script>
<script src="{{asset('unit-portal/js/jquery-clockpicker.min.js')}}"></script>
<script src="{{asset('client-portal/js/select2.full.min.js')}}"></script>
<script src="{{asset('unit-portal/js/sweetalert2.min.js')}}"></script>
<script src="{{asset('unit-portal/js/logout.js')}}"></script>
<script src="{{asset('unit-portal/js/global.js')}}"></script>
<script src="{{asset('client-portal/js/adminlte.js')}}"></script>
<script src="{{asset('client-portal/js/bootbox.min.js')}}"></script>
<script src="{{asset('client-portal/js/bootstrap.bundle.js')}}"></script>
<script src="{{asset('client-portal/js/jquery.overlayScrollbars.min.js')}}"></script>
<script src="{{asset('client-portal/js/tempusdominus-bootstrap-4.min.js')}}"></script>
<script src="{{asset('client-portal/js/daterangepicker.js')}}"></script>

<!--HighChart -->
<script src="{{asset('unit-portal/js/highcharts.js')}}"></script>
<script src="{{asset('unit-portal/js/exporting.js')}}"></script>
<script src="{{asset('unit-portal/js/export-data.js')}}"></script>
<script src="{{asset('unit-portal/js/variable-pie.js')}}"></script>
{{-- <script src="{{asset('unit-portal/js/budgetView.js')}}"></script> --}}
<script src="{{asset('unit-portal/js/login.js')}}"></script>

<script src="{{asset('unit-portal/js/jquery.min.js')}}"></script>
<script src="{{asset('unit-portal/js/bootstrap.min.js')}}"></script>

@yield('scripts')

</html>