<?php
namespace App\Http\Controllers\UnitPortal;


use App\Http\Controllers\Controller;
use App\Events\NewChatMessageFromUnit;
use App\Events\NewBookingCreatedEventFromAllPortal;
use App\Models\Shift;
use App\Models\StaffCategory;
use App\Models\Booking;
use App\Models\ClientUnitSchedule;
use App\Models\ClientUnitChat;
use App\Models\ClientUnitContact;
use App\Models\ClientUnit;
use App\Models\Admin;
use App\Models\ClientUnitPayment;
use App\Http\Controllers\UnitPortal\UnitHelper;
use App\Listeners\NewCancelFromUnit;
use App\Mail\SendAmendMsg;
use App\Models\BookingLog;
use App\Models\Notification;
use App\Models\Quotation;
use App\Models\TaxWeek;
use App\Models\BookingUnitAmend;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\ClientUnitAnnualBudget;
use \Swift_Mailer;
use Swift_SmtpTransport;
use Symfony\Component\Mailer\Transport\Smtp\SmtpTransport;
use App\Mail\SendPlainSMS as SendPlainSMS;
use App\Mail\Amend;
use Carbon\Carbon;
use DateTime;
use DateInterval;
use DatePeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Services\UnitHelperService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class BookedShiftController extends Controller
{

  protected $unitHelper;

  public function __construct(UnitHelperService $unitHelper)
  {
      $this->unitHelper = $unitHelper;
      if (!Auth::check()) {
        return redirect()->route('login');
      }
  }
    // public function list(){
    //     $currentDateTime = Carbon::now()->format('Y-m-d H:i:s'); 
    //     $unitHelper = app(UnitHelperService::class);
    //     $events = $unitHelper->getComingEvents();
    //     $shifts = Shift::whereNotIn('shiftId',[6])->get();
    //     $categories = StaffCategory::whereIn('categoryId',[1,2,3])->get();
    //     $loggedInPortalId = Auth::user()->portal_id;
    //     $unitsdropdown = ClientUnit::where('status', 1)
    //     ->where('clientId', $loggedInPortalId)
    //     ->get();

    //     $query = Booking::with('category','staff','unit','shift','timesheet');
    //     if(Auth::user()->userable_type == 'App\Models\Client'){
    //       $query = $query->whereIn('unitId',function($type) use ($loggedInPortalId){
    //         $type->select('clientUnitId')
    //         ->from('client_units')->whereNotIn('clientId',[16,37,44,25])->where('clientId',$loggedInPortalId);
    //       })
    //       ->orderBy('date','ASC')
    //       ->orderBy('unitStatus','DESC')
    //       ->orderBy('shiftId','ASC');
    //     }else{
    //       $query->where('unitId',$loggedInPortalId)
    //       ->orderBy('date','ASC')
    //       ->orderBy('unitStatus','DESC')
    //       ->orderBy('shiftId','ASC');
    //     }
    //     if (request()->filled('unitId')) {
    //       $query->where('unitId', request('unitId')); // Apply unit filter
    //   }
    //     $dates=[];
    //     if(request()->has('date')){
    //         $dates = explode(" - ",request('date'));
    //         $query->whereBetween('date',[date('Y-m-d',strtotime($dates[0])),date('Y-m-d',strtotime($dates[1]))]);
    //     }else{
    //         $query->whereBetween('date',[date('Y-m-d'),date('Y-m-d', strtotime('+15 days'))]);
    //     }

    //     if(request()->has('shift') && !empty(request('shift'))){
    //         $query->where('shiftId',request('shift'));
    //     }

    //     if(request()->has('category') && !empty(request('category'))){
    //         $query->where('categoryId',request('category'));
    //     }
    //     if(request()->has('status') && !empty(request('status'))){
    //       if(request('status')==7){
    //         $query->whereIn('unitStatus',[0,1,2,3,4,5,6]);
    //       }else if(request('status')==8){
            
    //         $query->whereRaw(
    //               DB::raw("STR_TO_DATE(CONCAT(date, ' ', end_time), '%Y-%m-%d %H:%i:%s') < '{$currentDateTime}'") // ✅ changed to DB::raw
    //           )
    //           ->whereIn('unitStatus', [1, 4])
    //           ->where('staffStatus', 3);
            
    //       }else if(request('status')==9){

            
    //         $query->whereHas('timesheet', function ($q) {
    //           $q->whereNotNull('image');
    //         });
            
    //       }else if(request('status')==10){
    //         $query->whereHas('timesheet', function ($q) {
    //           $q->where('status',2);
    //         });
    //       }else{
    //         $query->where('unitStatus',request('status'));
    //       }

    //     }else{
    //       $query->whereIn('unitStatus',[4,1]);
    //     }
        
    //     if(request('per_page')){
    //       $bookings = $query->paginate(request('per_page'));
    //     }else{
    //       $bookings = $query->paginate(15);
    //     }
    //     if (Auth::user()->userable_type == 'App\Models\Client') {
    //         $unitIds = \App\Models\ClientUnit::whereNotIn('clientId', [16, 37, 44, 25]) 
    //             ->where('clientId', $loggedInPortalId)
    //             ->pluck('clientUnitId'); 

    //         $scheduleAll = ClientUnitSchedule::whereIn('clientUnitId', $unitIds)->get(); 
    //     } else {
    //         $scheduleAll = ClientUnitSchedule::where('clientUnitId', $loggedInPortalId)->get();
    //     }

    //     foreach ($bookings as $booking) {
    //       $schedule = $scheduleAll->where('shiftId',$booking->shiftId)->where('staffCategoryId',$booking->categoryId)->first();
    //         if(isset($schedule)){
    //         $bookDateTime = $booking->date." ".$schedule->startTime;
    //         $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
    //         $created = Carbon::createFromFormat('Y-m-d H:i:s', $booking->created_at);
    //         $booking->diff_in_minutes = $created->diffInRealHours($to);
    //         }
    //         $booking->details = $this->getDetailsColumn($booking);
    //         $booking->unitStatusNumber = $booking->unitStatus;
    //         $booking->unitStatus = $this->getStatusColumn($booking);

    //         $booking->costPerShift = $this->calculateCost($booking);
    //         $booking->profile = $this->profileBtn($booking);
    //     }
    //     $format = "Y-m-d";
    //     $begin = new DateTime(date('Y-m-d'));
    //     $end = new DateTime(date('Y-m-d', strtotime('+12 day')));
    //     $interval = new DateInterval('P1D'); // 1 Day
    //     $dateRange = new DatePeriod($begin, $interval, $end);
    //     $range = [];
    //     foreach ($dateRange as $date) {
    //         $range[] = $date->format($format);
    //     }
    //     $bookSumry = Booking::whereBetween('date',[date('Y-m-d'),date('Y-m-d', strtotime('+12 day'))])
    //                  ->where('unitId','<>',21)
    //                  ->where('unitId',$loggedInPortalId)
    //                  // ->where('unitStatus',4)
    //                  ->get();
    //     $summary = [];
    //     for($i=0;$i<count($range);$i++) {
    //       $bookCount[$i] = $bookSumry->where('date',$range[$i]);
    //       $summary[$i]['date'] = $range[$i];
    //       $summary[$i]['count'] = $bookCount[$i]->count();
    //       if($dates!=Null){
    //         if(date('Y-m-d',strtotime($dates[0]))==date('Y-m-d',strtotime($dates[1])) && date('Y-m-d',strtotime($dates[0]))==$range[$i] ){
    //             $summary[$i]['selection'] = 1;
    //         }else{
    //           $summary[$i]['selection'] = 0;
    //         }
    //       }else{
    //         $summary[$i]['selection'] = 0;
    //       }
    //     }

    //     return view('unitPortal.bookedShift',compact('shifts','unitsdropdown','categories','summary','bookings','events'));
    // }
    public function list(){
      $currentDateTime = Carbon::now()->format('Y-m-d H:i:s'); 
      $unitHelper = app(UnitHelperService::class);
      $events = $unitHelper->getComingEvents();
      $shifts = Shift::whereNotIn('shiftId',[6])->get();
      $categories = StaffCategory::whereIn('categoryId',[1,2,3])->get();
      $loggedInPortalId = Auth::user()->portal_id;

      $unitsdropdown = ClientUnit::where('status', 1)
          ->where('clientId', $loggedInPortalId)
          ->get();

      $query = Booking::with('category','staff','unit','shift','timesheet');
      if(Auth::user()->userable_type == 'App\Models\Client'){
          $query = $query->whereIn('unitId',function($type) use ($loggedInPortalId){
              $type->select('clientUnitId')
                  ->from('client_units')->whereNotIn('clientId',[16,37,44,25])->where('clientId',$loggedInPortalId);
          });
      } else {
          $query->where('unitId',$loggedInPortalId);
      }
      
      // Apply sorting based on request parameter
      if(request()->has('sort') && !empty(request('sort'))){
          switch(request('sort')){
              case 'date_asc':
                  $query->orderBy('date', 'ASC')->orderBy('bookingId', 'DESC');
                  break;                $query->orderBy('date', 'ASC')
                      ->orderByRaw('CASE 
                          WHEN shiftId = 1 THEN 1   -- Early
                          WHEN shiftId = 2 THEN 2   -- Late  
                          WHEN shiftId = 3 THEN 3   -- Long Day
                          WHEN shiftId = 4 THEN 4   -- Night
                          ELSE 5 END')
                      ->orderBy('bookingId', 'DESC');
                break;
              case 'date_desc':
                $query->orderBy('date', 'DESC')
                      ->orderByRaw('CASE 
                          WHEN shiftId = 1 THEN 1   -- Early
                          WHEN shiftId = 2 THEN 2   -- Late  
                          WHEN shiftId = 3 THEN 3   -- Long Day
                          WHEN shiftId = 4 THEN 4   -- Night
                          ELSE 5 END')
                      ->orderBy('bookingId', 'DESC');
                break;
              case 'details_asc':
                  $query->orderBy('created_at', 'ASC')->orderBy('bookingId', 'DESC');
                  break;
              case 'details_desc':
                  $query->orderBy('created_at', 'DESC')->orderBy('bookingId', 'DESC');
                  break;
              default:
                $query->orderBy('date', 'DESC')
                      ->orderByRaw('CASE 
                          WHEN shiftId = 1 THEN 1   -- Early
                          WHEN shiftId = 2 THEN 2   -- Late  
                          WHEN shiftId = 3 THEN 3   -- Long Day
                          WHEN shiftId = 4 THEN 4   -- Night
                          ELSE 5 END')
                      ->orderBy('bookingId','DESC');          }
      } else {
            $query->orderBy('date', 'DESC')
              ->orderByRaw('CASE 
                  WHEN shiftId = 1 THEN 1   -- Early
                  WHEN shiftId = 2 THEN 2   -- Late  
                  WHEN shiftId = 3 THEN 3   -- Long Day
                  WHEN shiftId = 4 THEN 4   -- Night
                  ELSE 5 END')
              ->orderBy('bookingId','DESC');
      }

      // ...existing code for filters...
      
      if (request()->filled('unitId')) {
          $query->where('unitId', request('unitId'));
      }

      $dates = [];
      if(request()->has('date')){
          $dates = explode(" - ", request('date'));
          if(count($dates) >= 2) {
              $query->whereBetween('date', [date('Y-m-d', strtotime($dates[0])), date('Y-m-d', strtotime($dates[1]))]);
          }
      } 

      if(request()->has('shift') && !empty(request('shift'))){
          $query->where('shiftId', request('shift'));
      }

      if(request()->has('category') && !empty(request('category'))){
          $query->where('categoryId', request('category'));
      }

      if(request()->has('status') && !empty(request('status'))){
          if(request('status') == 7){
              $query->whereIn('unitStatus', [0,1,2,3,4,5,6,7,8,9]);
          } else if(request('status') == 8){
              $query->whereRaw(
                  DB::raw("STR_TO_DATE(CONCAT(date, ' ', end_time), '%Y-%m-%d %H:%i:%s') < '{$currentDateTime}'")
              )
              ->whereIn('unitStatus', [1, 4])
              ->where('staffStatus', 3);
          } else if(request('status') == 9){
              $query->whereHas('timesheet', function ($q) {
                  $q->whereNotNull('image');
              });
          } else if(request('status') == 10){
              $query->whereHas('timesheet', function ($q) {
                  $q->where('status', 2);
              });
          } else {
              $query->where('unitStatus', request('status'));
          }
      } else {
          $query->whereIn('unitStatus', [4,1]);
      }
      
      // Only show bookings with start_time (to match the view condition)
      $query->whereNotNull('start_time');
      
      // Handle pagination with validation
      $perPage = request('per_page', 15); // Default to 15
      $allowedPerPage = [15, 20, 50, 100];
      if (!in_array($perPage, $allowedPerPage)) {
          $perPage = 15; // Default if invalid value
      }
      
      $bookings = $query->paginate($perPage);

      // if(request('per_page')){
      //     $bookings = $query->paginate(request('per_page'));
      // } else
      //  {
      //     $bookings = $query->paginate(500);
      // }
  
      if (Auth::user()->userable_type == 'App\Models\Client') {
          $unitIds = \App\Models\ClientUnit::whereNotIn('clientId', [16, 37, 44, 25]) 
              ->where('clientId', $loggedInPortalId)
              ->pluck('clientUnitId'); 
  
          $scheduleAll = ClientUnitSchedule::whereIn('clientUnitId', $unitIds)->get(); 
      } else {
          $scheduleAll = ClientUnitSchedule::where('clientUnitId', $loggedInPortalId)->get();
      }
  
      $contactMailId = '';
      foreach ($bookings as $booking) {
          $contact = "";
          if (!empty($booking->requestedBy)) {
              if (is_numeric($booking->requestedBy)) {
                  $unitContact = ClientUnitContact::find($booking->requestedBy);
                  if ($unitContact) {
                      $contact = $unitContact->fullName;
                  }
              } else {
                  $contact = $booking->requestedBy;
              }
          }
          if (isset($unitContact->email) && $unitContact->email == null) {
              $unitContact->email = '';
          }
          if (isset($unitContact->email)) $contactMailId = $unitContact->email;
          else $contactMailId = '';
          if ($booking->type == 2) {
              $from = "Online";
          } else {
              if ($booking->modeOfRequest == 1) {
                  $from = "Email";
              } elseif ($booking->modeOfRequest == 2) {
                  $from = "Phone";
              } elseif ($booking->modeOfRequest == 3) {
                  $from = "SMS";
              }
          }
          $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->categoryId)
              ->where('shiftId', $booking->shiftId)->first();
          $html = "";
          if (isset($schedule->startTime)) {
              $bookDateTime = $booking->date . " " . $schedule->startTime;
              $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
              $created = Carbon::createFromFormat('Y-m-d H:i:s', $booking->created_at);
              $diff_in_minutes = $created->diffInRealHours($to);
              if ($diff_in_minutes < 48) $clasRed = "redClr";
              else $clasRed = "";
              $html = "";
              $html .= "<div class='font10'>" . $contact . " | ";
              $html .= $from . "</div>";
              $html .= "<div class='font10'><div class='" . $clasRed . "'>Notice Given - <strong class='hourstostart'>" . round($diff_in_minutes) .
                  " Hrs</div></strong></div>";
              $html .= "<div class='font10'> " . $booking->reason_text . "</div>";
              $html .= "<div class='font10'>" . date('d-m-Y H:i', strtotime($booking->created_at)) . "</div>";
              $booking->totalhoursto = round($diff_in_minutes);
          }else{
              $booking->totalhoursto=0;
          }
          $booking->details = $html;
          $booking->costPerShift = $this->calculateCost($booking);
          $booking->costPerShiftNumberonly = $this->calculateCostBookingNumberonly($booking);
          $booking->profile = $this->profileBtn($booking);
      }
  
      // ✅ CHANGED: Create the fixed date range (this is fine)
      $format = "Y-m-d";
      $begin = new DateTime(date('Y-m-d'));
      $end = new DateTime(date('Y-m-d', strtotime('+12 day')));
      $interval = new DateInterval('P1D'); // 1 Day
      $dateRange = new DatePeriod($begin, $interval, $end);
      $range = [];
      foreach ($dateRange as $date) {
          $range[] = $date->format($format);
      }
  
      // ✅ ADDED: Fresh summary query without date filters
      $summaryQuery = Booking::with('timesheet');
  
      if(Auth::user()->userable_type == 'App\Models\Client'){
          $summaryQuery->whereIn('unitId', function($type) use ($loggedInPortalId){
              $type->select('clientUnitId')
                  ->from('client_units')->whereNotIn('clientId', [16, 37, 44, 25])
                  ->where('clientId', $loggedInPortalId);
          });
      } else {
          $summaryQuery->where('unitId', $loggedInPortalId);
      }
  
      if(request()->filled('unitId')){
          $summaryQuery->where('unitId', request('unitId'));
      }
      if(request()->has('shift') && !empty(request('shift'))){
          $summaryQuery->where('shiftId', request('shift'));
      }
      if(request()->has('category') && !empty(request('category'))){
          $summaryQuery->where('categoryId', request('category'));
      }
      if(request()->has('status') && !empty(request('status'))){
          if(request('status') == 7){
              $summaryQuery->whereIn('unitStatus', [0,1,2,3,4,5,6,7,8,9]);
          } else if(request('status') == 8){
              $summaryQuery->whereRaw(
                  DB::raw("STR_TO_DATE(CONCAT(date, ' ', end_time), '%Y-%m-%d %H:%i:%s') < '{$currentDateTime}'")
              )
              ->whereIn('unitStatus', [1, 4])
              ->where('staffStatus', 3);
          } else if(request('status') == 9){
              $summaryQuery->whereHas('timesheet', function ($q) {
                  $q->whereNotNull('image');
              });
          } else if(request('status') == 10){
              $summaryQuery->whereHas('timesheet', function ($q) {
                  $q->where('status', 2);
              });
          } else {
              $summaryQuery->where('unitStatus', request('status'));
          }
      } else {
          $summaryQuery->whereIn('unitStatus', [4,1]);
      }
  
      // Only show bookings with start_time (to match the main query and view condition)
      $summaryQuery->whereNotNull('start_time');
  
      // ✅ ADDED: Apply only fixed 12-day range to summary
      $bookSumry = $summaryQuery
          ->whereBetween('date', [date('Y-m-d'), date('Y-m-d', strtotime('+12 day'))])
          ->get();
  
      $summary = [];
      for($i=0; $i<count($range); $i++) {
          $bookCount[$i] = $bookSumry->where('date', $range[$i]);
          $summary[$i]['date'] = $range[$i];
          $summary[$i]['count'] = $bookCount[$i]->count();
  
          if (!empty($dates) && count($dates) >= 2) {
              if (date('Y-m-d', strtotime($dates[0])) == date('Y-m-d', strtotime($dates[1])) && date('Y-m-d', strtotime($dates[0])) == $range[$i]) {
                  $summary[$i]['selection'] = 1;
              } else {
                  $summary[$i]['selection'] = 0;
              }
          } else {
              $summary[$i]['selection'] = 0;
          }
      }
  
      return view('unitPortal.bookedShift', compact('shifts', 'unitsdropdown', 'categories', 'summary', 'bookings', 'events'));
  }
  
  public function export(){
      $loggedInPortalId = Auth::user()->portal_id;
      
      $query = Booking::with('category','staff','unit','shift');
      if(Auth::user()->userable_type == 'App\Models\Client'){
          $query = $query->whereIn('unitId',function($type) use ($loggedInPortalId){
              $type->select('clientUnitId')
                  ->from('client_units')->whereNotIn('clientId',[16,37,44,25])->where('clientId',$loggedInPortalId);
          })
          ->orderBy('bookingId','DESC');
      } else {
          $query->where('unitId',$loggedInPortalId)
              ->orderBy('bookingId','DESC');
      }
      
      // Apply same sorting as in list method
      if(request()->has('sort') && !empty(request('sort'))){
          switch(request('sort')){
              case 'date_asc':
                  $query->orderBy('date', 'ASC')->orderBy('bookingId', 'DESC');
                  break;
              case 'date_desc':
                  $query->orderBy('date', 'DESC')->orderBy('bookingId', 'DESC');
                  break;
              case 'details_asc':
                  $query->orderBy('created_at', 'ASC')->orderBy('bookingId', 'DESC');
                  break;
              case 'details_desc':
                  $query->orderBy('created_at', 'DESC')->orderBy('bookingId', 'DESC');
                  break;
              default:
                  $query->orderBy('bookingId','DESC');
          }
      } else {
          $query->orderBy('bookingId','DESC');
      }
      
      // Apply same filters as in list method
      if (request()->filled('unitId')) {
          $query->where('unitId', request('unitId'));
      }
      
      if(request()->has('date')){
          $dates = explode(" - ", request('date'));
          if(count($dates) >= 2) {
              $query->whereBetween('date', [date('Y-m-d', strtotime($dates[0])), date('Y-m-d', strtotime($dates[1]))]);
          }
      }
      
      if(request()->has('shift') && !empty(request('shift'))){
          $query->where('shiftId', request('shift'));
      }
      
      if(request()->has('category') && !empty(request('category'))){
          $query->where('categoryId', request('category'));
      }
      
      if(request()->has('status') && !empty(request('status'))){
          if(request('status') != 7){
              $query->where('unitStatus', request('status'));
          }
      } else {
          $query->whereIn('unitStatus', [4,1]);
      }
      
      // Only show bookings with start_time
      $query->whereNotNull('start_time');
      
      $bookings = $query->get();
      
      // Create Excel file
      $filename = 'booked_shifts_' . date('Y-m-d_H-i-s') . '.csv';
      
      // Set headers for download
      header('Content-Type: text/csv');
      header('Content-Disposition: attachment;filename="' . $filename . '"');
      header('Cache-Control: max-age=0');
      
      // Create CSV content
      $output = fopen('php://output', 'w');
      
      // Add BOM for UTF-8 Excel compatibility
      fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
      
      // Add headers - matching table headers exactly
      fputcsv($output, [
          'Booking ID',
          'Units', 
          'Date',
          'Shift',
          'Category',
          'Staff',
          'Profile',
          'Status',
          'Details'
      ]);
      
      // Add data rows
      foreach ($bookings as $booking) {
          // Get staff name
          if($booking->staffId != null) {
              $staffName = $booking->staff->full_name;
              if($booking->unitStatus == 2) {
                  $staffName .= ' (Cancelled)';
              } elseif($booking->inform_status == 2) {
                  $staffName .= ' (Acknowledged)';
              }
          } else {
              if($booking->unitStatus == 2) {
                  $staffName = 'Cancelled';
              } else {
                  $staffName = 'Unassigned';
              }
          }
          
          // Get status
          $status = '';
          switch($booking->unitStatus) {
              case 2:
                  $status = 'Cancelled';
                  break;
              case 4:
                  $status = 'Confirmed';
                  break;
              case 1:
                  $status = 'Awaiting Approval';
                  break;
              default:
                  $status = 'Unknown';
          }
          
          // Get details (clean version for export)
          $details = $this->getDetailsColumnForExport($booking);
          
          fputcsv($output, [
              $booking->bookingId,
              $booking->unit->name,
              date('d-M-Y D', strtotime($booking->date)),
              $booking->shift->name,
              $booking->category->name,
              $staffName,
              strip_tags($booking->profile ?? ''),
              $status,
              $details
          ]);
      }
      
  }
  
  /**
   * Get details column for Excel export (clean text version)
   */
  public function getDetailsColumnForExport($booking){
      $contact = "";
      if(!empty($booking->requestedBy)){
          if(is_numeric($booking->requestedBy)){
              $unitContact = ClientUnitContact::find($booking->requestedBy);
              if($unitContact){
                  $contact = $unitContact->fullName;
              }
          } else {
              $contact = $booking->requestedBy;
          }
      }
      
      $from = '';
      if($booking->type == 2){
          $from = "Online";
      } else {
          if($booking->modeOfRequest == 1){
              $from = "Email";
          } elseif($booking->modeOfRequest == 2){
              $from = "Phone";
          } elseif($booking->modeOfRequest == 3){
              $from = "SMS";
          }
      }
      
      $schedule = ClientUnitSchedule::where('staffCategoryId',$booking->categoryId)
                  ->where('shiftId',$booking->shiftId)->first();
      $diff_in_minutes = 0;
      if(isset($schedule)){
          $bookDateTime = $booking->date." ".$schedule->startTime;
          $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
          $created = Carbon::createFromFormat('Y-m-d H:i:s', $booking->created_at);
          $diff_in_minutes = $created->diffInRealHours($to);
      }
      
      // Create clean text version for export
      $details = $contact . " | " . $from . " | ";
      $details .= "Notice Given - " . round($diff_in_minutes) . "Hrs | ";
      $details .= $booking->reason_text . " | ";
      $details .= date('d-m-Y H:i', strtotime($booking->created_at));
      
      return $details;
  }

  public function getDetailsColumn($booking){
        $contact = "";
      if(!empty($booking->requestedBy)){
        if(is_numeric($booking->requestedBy)){
            $unitContact = ClientUnitContact::find($booking->requestedBy);
          if($unitContact){
            $contact =$unitContact->fullName;
          }
        }else{
          $contact=$booking->requestedBy;
        }
      }
      $from = '';
      if($booking->type==2){
        $from = "Online";
      }else{
        if($booking->modeOfRequest==1){
          $from= "Email";
        }
        elseif($booking->modeOfRequest==2){
          $from= "Phone";
        }
        elseif($booking->modeOfRequest==3){
          $from= "SMS";
        }
      }
      //$schedule = ClientUnitSchedule::where('clientUnitId',auth()->guard('unit')->user()->clientUnitId)
      $schedule = ClientUnitSchedule::where('staffCategoryId',$booking->categoryId)
                  ->where('shiftId',$booking->shiftId)->first();
      $diff_in_minutes= 0;
      $clasRed = '';
      if(isset($schedule)){
        $bookDateTime = $booking->date." ".$schedule->startTime;
        $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
        $created = Carbon::createFromFormat('Y-m-d H:i:s', $booking->created_at);
        $diff_in_minutes = $created->diffInRealHours($to);
       
      }
      if($diff_in_minutes <= 48) $clasRed = "redClr"; else $clasRed="";
      $html ="";
      
      $html .="<div class='font10'>".$contact." | ";
      $html .= $from."</div>";
      $html .="<div class='font10'><span class='".$clasRed."'>Notice Given - ".round($diff_in_minutes)."Hrs</span></div>";
      $html .="<div class='font10'> ".$booking->reason_text."</div>";
      $html .="<div class='font10'>".date('d-m-Y H:i',strtotime($booking->created_at))."</div>";
      return $html;
    }

    public function profileBtn($booking){
      if($booking->staffId){
        if($booking->unitStatus == 2 || $booking->unitStatus == 3) { // if cancelled or unable to cover
          $html = '<i class="fa fa-ban" aria-hidden="true"></i>';
        } else {
          if($booking->unit->clientId == 29){
            $html = "<a target='_blank' href='https://nursesgroupadmin.co.uk/storage/app/staff/staff_profile/".$booking->staff->profileDocumentFile."' class='profile'><i class='fa fa-user' aria-hidden='true'></i>
            </a>";
          }else{
            $html = "<a target='_blank' href='https://nursesgroupadmin.co.uk/storage/app/staff/staff_profile/".$booking->staff->profileDocumentFile."' class='profile'><i class='fa fa-user' aria-hidden='true'></i></a>";
          }
        }
      }else{
        if($booking->unitStatusNumber == 2 || $booking->unitStatusNumber == 3) {
          $profile = '<i class="fa fa-ban" aria-hidden="true"></i>';
        }else{
          $profile = '<i class="fa fa-search" aria-hidden="true"></i>';
        }
        $html = "<a style='background:#fff;' href='javascript:void(0)' class='profile' >".$profile."</a>";
      }
      return $html;
    }

    public function calculateCost($booking){
     // $allClientPayments = ClientUnitPayment::where('clientUnitId',auth()->guard('unit')->user()->clientUnitId)->get();
     $allClientPayments = ClientUnitPayment::get();
      $day = strtolower(date('D',strtotime($booking->date)));
      $shiftId = $booking->shift->shiftId;
      $clientPayment = $allClientPayments->where('staffCategoryId',$booking->category->categoryId)->where('rateType',1);
      $filteredData = $clientPayment->first();

      if (!is_null($filteredData) && isset($filteredData['taPerMile'], $filteredData['taNoOfMiles'])) {
        $ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];
      } else {
          $ta = 0; // Or handle it however you want
      }


      $clientPaymentsEnic = $allClientPayments->where('staffCategoryId',$booking->category->categoryId)->where('rateType',2);
      $filteredDataEnic = $clientPaymentsEnic->first();

      switch ($day) {
          case "mon":
              $selectColumn = "Monday";
              break;
          case "tue":
              $selectColumn = "Tuesday";
              break;
          case "wed":
              $selectColumn = "Wednesday";
              break;
          case "thu":
              $selectColumn = "Thursday";
              break;
          case "fri":
              $selectColumn = "Friday";
              break;
          case "sat":
              $selectColumn = "Saturday";
              break;
          case "sun":
              $selectColumn = "Sunday";
              break;
          }

      if($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
          $columnName = "day".$selectColumn;
      } else if($shiftId == 5) {
        $columnName = "twilight_".strtolower($selectColumn);
      } else {
          $columnName = "night".$selectColumn;
      }

      $payAmountPerHr = isset($filteredData[$columnName]) ? $filteredData[$columnName] : null;

      if($booking->date=="2019-04-18" && $shiftId == 4){
        $payAmountPerHr = $payAmountPerHr*1.5;
      }
      if($booking->date=="2019-04-19"){
        $payAmountPerHr = $payAmountPerHr*1.5;
      }
      if($booking->date=="2019-04-21" && $shiftId == 4){
        $payAmountPerHr = $payAmountPerHr*1.5;
      }
      if($booking->date=="2019-04-22"){
        $payAmountPerHr = $payAmountPerHr*1.5;
      }
      $clientPaymentsEnicRate = isset($filteredDataEnic[$columnName]) ? $filteredDataEnic[$columnName] : null;

      $schedule = ClientUnitSchedule::where('clientUnitId',$booking->unitId)->where('staffCategoryId',$booking->category->categoryId)->where('shiftId',$booking->shift->shiftId)->first();
      if(isset($schedule->totalHoursUnit)){
        $totalHoursUnit = $schedule->totalHoursUnit;
      }else{
        $totalHoursUnit = 0;
      }

      $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate)+$ta;
      return  "<a href='javascript:void(0)' price='£ ".number_format($inTotal,2)."' class='view showPrice'><i class='fa fa-search' aria-hidden='true'></i>
      </a>";
    }

    public function getStatusColumn($booking){
        $html = '';
      switch ($booking->unitStatus) {
        case '2':
          $html .= "<a href='#' class='cancel'>Cancelled</a>";

          break;
        case '4':
          $html .= "<a href='#' class='confrimd'>Confirmed</a> ";

          break;
        case '1':
          $html .= "<a href='#' class='awaiting'>Awaiting Approval</a> ";

          break;
      }
      return $html;
    }

    public function getBooking(){
      $booking = Booking::with(['unit','category','shift','requestedbyrelation','bookedby'])->find(request('bookingId'));
      $times = ClientUnitSchedule::where('clientUnitId',$booking->unitId)->where('staffCategoryId',$booking->categoryId)->where('shiftId',$booking->shiftId)->first();
      if($times){
        $startTime = $times->startTime;
      }
      $booking->date = date('Y-m-d',strtotime($booking->date));
      $booking->day = date('d-M-Y, D',strtotime($booking->date));
      $amend = BookingUnitAmend::where('bookingId',request('bookingId'))->first();
      return [
        'amend'=> $amend,
        'booking'=> $booking,
        'start_time' => $startTime,
        'now' => date('Y-m-d H:i:s'),
      ];
    }

    public function doCancel(Request $request){
      $bookingId = request('bookingId');
      $booking = Booking::find($bookingId);
      $notes = request('notes');
      // Update weekly utilisation for cancellation
      $this->updateWeeklyUtilisationForCancellation($booking);
      $input = [
        'categoryId'=>$booking->categoryId,
        'date'=>$booking->date,
        'unitId'=>40,
        'unitStatus'=>4,
        'staffStatus'=>3,
        'requestedBy'=>1,
        'start_time'  =>$booking->start_time,
        'end_time'  =>$booking->end_time,
        'staffId' => $booking->staffId,
        'shiftId'=>$booking->shiftId,
        'confirmedBy'=>auth()->user()->userable_id,
        'cancelInformUnitTo' => 0,
        'modeOfRequest' => 1,
        'booked_user_id' => auth()->user()->userable_id,
        'booked_portal' => auth()->user()->portal_type,
        'booked_portal_id' => auth()->user()->portal_id
      ];
      if(isset($booking->categoryId) && !empty($booking->staffId)){
        $newBook = Booking::create($input);
        $invoice = Quotation::firstOrCreate([
          'bookingId' =>$newBook->bookingId,
        ]);
        $taxWeek = TaxWeek::where('date',$booking->date)->first();
        if(isset($taxWeek->weekNumber)) $week = $taxWeek->weekNumber;
        $invoice->update([
          'year' =>date('Y',strtotime($newBook->date)),
          'month' =>date('m',strtotime($newBook->date)),
          'week'  =>$week,
        ]);
        BookingLog::create([
          'bookingId' =>$newBook->bookingId,
          'content' =>'Booking <span class="logHgt">Created </span> based on cancellation of  <strong>'.$booking->bookingId."</strong> - Cancelled By ".auth()->user()->name."</strong> was assigned <strong>".$newBook->staff->full_name."</strong> Notes: ".$notes,
          'author' =>1,
            ]);
       ClientUnitChat::chatSave($booking->unitId,"Requested shift cancellation- ".$notes,$bookingId,false);

       // Add chat log for the newly created confirmed shift
       //ClientUnitChat::chatSave($newBook->unitId,"New confirmed shift created from cancelled booking #".$booking->bookingId,$newBook->bookingId,false);
      }

      $booking->update([
        'staffId'=>NULL,
        'staffStatus'=>4,
        'unitStatus' => 2,
        'modeOfCancelRequest' =>4,
        'cancelNotes' => request('notes'),
        'cancelDate'  =>date('Y-m-d'),
        'cancelTime'  =>date('H:i:s'),
        'cancelAuthorizedBy'  =>auth()->user()->userable_id,
        'cancelRequestedBy'=>request('requestedName'),
      ]);

      BookingLog::create([
        'bookingId' =>request('bookingId'),
        'content' =>'Booking <span class="logHgt">Cancelled </span> from <strong>'.auth()->user()->portal->name." -
         ".auth()->user()->name."</strong></strong>",
        'author' =>1,
      ]);

      $admins = [1,11,9,18,20];
      for ($i=0; $i < count($admins); $i++) {
        Notification::create([
          'adminId' =>$admins[$i],
          'text' =>"Booking Cancelled from ".auth()->user()->portal->name." - ".auth()->user()->name,
        ]);
      }

      if(config('app.env')=='live'){
      //   $mailMessage = "Dear admin, The booking Id: ".request('bookingId')." is cancelled now";
      //   Mail::to('<EMAIL>')->queue(new Amend($mailMessage));
      //   $transport = new SmtpTransport('smtp.gmail.com', 465, 'ssl');
      //   $transport->setUsername("<EMAIL>");
      //   //$transport->setPassword('Gr8N$G2021');
      //   $transport->setPassword('zsnmggfpciqwrtvo');
      //   $gmail = new Swift_Mailer($transport);

      //   Mail::setSwiftMailer($gmail);
      //  // Mail::to("<EMAIL>")->queue(new SendPlainSMS($mailMessage));
      //  Mail::to("<EMAIL>")->queue(new SendPlainSMS($mailMessage));

      //   $msg = 'You are successfully amend the shift';
      //   session()->flash('alert', $msg);

      //   event(new NewCancelFromUnit($booking));
      }
      return ['status'=>true];
    }

    public function doAmendAction(Request $req) {
      $bookingDetails = Booking::with(['staff','unit','category','shift','requestedby'])->select('bookings.*')->leftJoin('client_units', 'client_units.clientUnitId', 'bookings.unitId')->where('bookingId',$req->bookingId)->first();
      $bookingDetails->date = date('Y-m-d',strtotime($bookingDetails->date));
      $bookingDetails->day = date('d-M-Y, D',strtotime($bookingDetails->date));
      Booking::find(request('bookingId'))->update([
        'unitAmend' =>1
      ]);
      // Update weekly utilisation for amendment
      $this->updateWeeklyUtilisationForAmendment($bookingDetails);
      BookingUnitAmend::create([
        'bookingId'=>request('bookingId'),
        'preview'=>request('amendPreview'),
        'notes'=>request('amendNotes'),
        'amendRequestedName'=>request('amendRequestedName'),
      ]);

      BookingLog::create([
        'bookingId' =>request('bookingId'),
        'content' =>'Booking <span class="logHgt">Amend </span> Requested from <strong>'.auth()->user()->portal->name."</strong>
        with message <strong>".request('amendPreview')."</strong>",
        'author' =>1,
        ]);
       
       // Use amend preview details instead of generic message
       $amendMessage = request('amendPreview') ?: 'Amendment requested';
       ClientUnitChat::chatSave($bookingDetails->unitId, $amendMessage, $bookingDetails->bookingId, false);


      $admins = [1,11,9,18,20];
      for ($i=0; $i < count($admins); $i++) {
        Notification::create([
          'adminId' =>$admins[$i],
          //'text' =>"Booking Amend Requested from ".auth()->guard('unit')->user()->unit->alias." - ".auth()->guard('unit')->user()->username." with message ".request('amendPreview'),
          'text' =>"Booking Amend Requested from  -  with message ".request('amendPreview'),
        ]);
      }
      $mailMessage = "Dear admin, The booking Id with detailed message: ".request('bookingId') ."|". $bookingDetails->date ."|". $bookingDetails->unit->name ."|".$bookingDetails->category->name ."|". $bookingDetails->shift->name .", Requested person ".request('amendRequestedName') ." with message ".request('amendNotes'). ", Date and Time ".date('d-M-Y H::m',strtotime($bookingDetails->created_at)). " is amended now";
      /*Mail::to('<EMAIL>')->queue(new SendAmendMsg($mailMessage));

      $transport = new SmtpTransport('smtp.gmail.com', 465, 'ssl');
      $transport->setUsername("<EMAIL>");
      $transport->setPassword('Gr8N$G2021');
      $gmail = new Swift_Mailer($transport);

      Mail::setSwiftMailer($gmail);
      Mail::to("<EMAIL>")->queue(new SendPlainSMS($mailMessage));
      Mail::to("<EMAIL>")->queue(new SendPlainSMS($mailMessage));


      $msg = 'You are successfully amend the shift';
      session()->flash('alert', $msg);*/

      //return ['status'=>$mailMessage];
      return ['status'=>true];
    }

    public function saveUnitChat(){
      $client_unit = ClientUnit::where('clientUnitId',request('unit_id'))->first();
      $chat = ClientUnitChat::create([
        'unit_id'=>request('unit_id'),
        'unit_name'=>$client_unit->name,
        'booking_id'=>request('booking_id'),
        'content'=>request('content'),
        'type'=>1,
        'color'=>1,
        ]);
        $user = auth()->user();
        if($user){
            $chat->author = $user->id;
            $chat->author_name = $user->name;
            $chat->save();
        }
      event(new NewChatMessageFromUnit($chat->chat_id));

      $mailMessage = "Dear Admin, The booking Id: ".request('booking_id')." has a new message from unit portal, Please check in Chat Log";
      // $transport = new SmtpTransport('smtp.gmail.com', 465, 'ssl');
      // $transport->setUsername("<EMAIL>");
      // //$transport->setPassword('Gr8N$G2021');
      // $transport->setPassword('zsnmggfpciqwrtvo');
      // $gmail = new Swift_Mailer($transport);

      // Mail::setSwiftMailer($gmail);
      // Mail::to("<EMAIL>")->queue(new SendPlainSMS($mailMessage,"New SMS From Unit Portal"));


      return ['status'=>true];
    }

    public function getUnitChat(){
      $client_unit_chats = ClientUnitChat::with('user.role')->where('booking_id',request('booking_id'))->latest()->get();
      return ['status'=>true,'data'=>$client_unit_chats];
    }
    public function sendAmendmessage($message)
    {
        return view('email.bookingAmend')
        ->with(['data' => $message]);
    }
     public function calculateCostBookingNumberonly($booking)
    {
        $allClientPayments = ClientUnitPayment::where('clientUnitId', 2)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();
        if (isset($filteredData->taPerMile) && isset($filteredData->taNoOfMiles)) {
            $ta = $filteredData->taPerMile * $filteredData->taNoOfMiles;
        } else {
            $ta = 0;
        }
        //$ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];

        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }

        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData[$columnName])) {
            $payAmountPerHr = $filteredData[$columnName];
        } else {
            $payAmountPerHr = 0;
        }
        //$payAmountPerHr = $filteredData[$columnName];
        if ($booking->date == "2019-04-18" && $shiftId == 4) {
            $payAmountPerHr = $payAmountPerHr * 1.5;
        }
        if ($booking->date == "2019-04-19") {
            $payAmountPerHr = $payAmountPerHr * 1.5;
        }
        if ($booking->date == "2019-04-21" && $shiftId == 4) {
            $payAmountPerHr = $payAmountPerHr * 1.5;
        }
        if ($booking->date == "2019-04-22") {
            $payAmountPerHr = $payAmountPerHr * 1.5;
        }
        if (isset($filteredDataEnic[$columnName])) {
            $clientPaymentsEnicRate = $filteredDataEnic[$columnName];
        } else {
            $clientPaymentsEnicRate = 0;
        }
        // $clientPaymentsEnicRate = $filteredDataEnic[$columnName];

        $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->category->categoryId)->where('shiftId', $booking->shift->shiftId)->first();
        if (isset($schedule->totalHoursUnit)) {
            $totalHoursUnit = $schedule->totalHoursUnit;
        } else {
            $totalHoursUnit = 0;
        }
        $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate) + $ta;
        return  number_format($inTotal, 2);
    }

    /**
     * Update weekly utilisation for a booking cancellation
     * 
     * @param Booking $booking The booking object
     * @return void
     */
    private function updateWeeklyUtilisationForCancellation(Booking $booking)
    {
        try {
            // Calculate financial year (April to March)
            $bookingYear = date('Y', strtotime($booking->date));
            $bookingMonth = date('n', strtotime($booking->date));
            $financialYear = $bookingMonth >= 4 ? $bookingYear : $bookingYear - 1;
            
            // Get the annual budget for this unit and financial year
            $clientUnitAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $booking->unitId)
                ->where('financial_year', $financialYear)
                ->first();
            
            if (!$clientUnitAnnualBudget) {
                Log::warning('No annual budget found for unit during cancellation', [
                    'unit_id' => $booking->unitId, 
                    'booking_id' => $booking->bookingId,
                    'financial_year' => $financialYear,
                    'booking_date' => $booking->date
                ]);
                return;
            }
            
            // Find the weekly budget where the booking date falls between week_start_date and week_end_date
            $clientUnitWeeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
                ->where('week_start_date', '<=', $booking->date)
                ->where('week_end_date', '>=', $booking->date)
                ->first();
            
            if (!$clientUnitWeeklyBudget) {
                Log::warning('No weekly budget found for booking cancellation date', [
                    'unit_id' => $booking->unitId,
                    'booking_id' => $booking->bookingId,
                    'booking_date' => $booking->date,
                    'financial_year' => $financialYear,
                    'annual_budget_id' => $clientUnitAnnualBudget->id
                ]);
                return;
            }
            
            // Calculate the cost of the booking
            $utilised_cost = $this->calculateCostBookingNumberonly($booking);
            
            // Update the weekly utilised fund using the global helper function
            updateWeeklyUtilisedFund(
                $clientUnitWeeklyBudget->id,
                $utilised_cost,
                'deduction',
                'App\Models\Booking',
                $booking->bookingId,
                'Booking cancelled - ID: ' . $booking->bookingId . ' on ' . $booking->date
            );
            
            Log::info('Weekly utilised fund updated for cancellation', [
                'weekly_budget_id' => $clientUnitWeeklyBudget->id,
                'booking_id' => $booking->bookingId,
                'booking_date' => $booking->date,
                'week_start_date' => $clientUnitWeeklyBudget->week_start_date,
                'week_end_date' => $clientUnitWeeklyBudget->week_end_date,
                'utilised_cost' => $utilised_cost
            ]);
            
        } catch (Exception $e) {
            Log::error('Error updating weekly utilised fund for booking cancellation', [
                'error' => $e->getMessage(),
                'booking_id' => $booking->bookingId,
                'unit_id' => $booking->unitId,
                'booking_date' => $booking->date
            ]);
        }
    }

    /**
     * Update weekly utilisation for a booking amendment
     * 
     * @param Booking $booking The booking object
     * @return void
     */
    private function updateWeeklyUtilisationForAmendment(Booking $booking)
    {
        try {
            // Calculate financial year (April to March)
            $bookingYear = date('Y', strtotime($booking->date));
            $bookingMonth = date('n', strtotime($booking->date));
            $financialYear = $bookingMonth >= 4 ? $bookingYear : $bookingYear - 1;
            
            // Get the annual budget for this unit and financial year
            $clientUnitAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $booking->unitId)
                ->where('financial_year', $financialYear)
                ->first();
            
            if (!$clientUnitAnnualBudget) {
                Log::warning('No annual budget found for unit during amendment', [
                    'unit_id' => $booking->unitId, 
                    'booking_id' => $booking->bookingId,
                    'financial_year' => $financialYear,
                    'booking_date' => $booking->date
                ]);
                return;
            }
            
            // Find the weekly budget where the booking date falls between week_start_date and week_end_date
            $clientUnitWeeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
                ->where('week_start_date', '<=', $booking->date)
                ->where('week_end_date', '>=', $booking->date)
                ->first();
            
            if (!$clientUnitWeeklyBudget) {
                Log::warning('No weekly budget found for booking amendment date', [
                    'unit_id' => $booking->unitId,
                    'booking_id' => $booking->bookingId,
                    'booking_date' => $booking->date,
                    'financial_year' => $financialYear,
                    'annual_budget_id' => $clientUnitAnnualBudget->id
                ]);
                return;
            }
            
            // Calculate the cost of the booking
            $utilised_cost = $this->calculateCostBookingNumberonly($booking);
            
            // Update the weekly utilised fund using the global helper function
            updateWeeklyUtilisedFund(
                $clientUnitWeeklyBudget->id,
                $utilised_cost,
                'deduction',
                'App\Models\Booking',
                $booking->bookingId,
                'Booking amended - ID: ' . $booking->bookingId . ' on ' . $booking->date
            );
            
            Log::info('Weekly utilised fund updated for amendment', [
                'weekly_budget_id' => $clientUnitWeeklyBudget->id,
                'booking_id' => $booking->bookingId,
                'booking_date' => $booking->date,
                'week_start_date' => $clientUnitWeeklyBudget->week_start_date,
                'week_end_date' => $clientUnitWeeklyBudget->week_end_date,
                'utilised_cost' => $utilised_cost
            ]);
            
        } catch (Exception $e) {
            Log::error('Error updating weekly utilised fund for booking amendment', [
                'error' => $e->getMessage(),
                'booking_id' => $booking->bookingId,
                'unit_id' => $booking->unitId,
                'booking_date' => $booking->date
            ]);
        }
    }


}