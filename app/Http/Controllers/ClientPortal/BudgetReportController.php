<?php

namespace App\Http\Controllers\ClientPortal;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\ClientUnitAnnualBudget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BudgetReportController extends Controller
{
    public function index(Request $request)
    {
        $user = \Illuminate\Support\Facades\Auth::user();
        if (!$user) {
            // Redirect to login or show an error
            return redirect()->route('login')->withErrors('You must be logged in to view this page.');
        }
        $clientId = $user->portal_id;
        // Get all units belonging to the client
        $units = \App\Models\ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        // Eager load annual budgets and weekly budgets for each unit
        $units->load(['annualBudgets' => function($q) {
            $q->select('id', 'client_unit_id', 'financial_year');
        }, 'annualBudgets.weeklyBudgets']);

        // Build filterData for frontend dynamic filters
        $filterData = [];
        foreach ($units as $unit) {
            $years = $unit->annualBudgets->pluck('financial_year')->unique()->values();
            $monthsByYear = [];
            $weeksByYearMonth = [];
            foreach ($unit->annualBudgets as $ab) {
                $weeks = $ab->weeklyBudgets;
                $months = $weeks->pluck('week_end_date')->map(function($date) { return (int)date('n', strtotime($date)); })->unique()->values();
                $monthsByYear[$ab->financial_year] = $months;
                foreach ($months as $m) {
                    $weeksByYearMonth[$ab->financial_year][$m] = $weeks->filter(function($w) use ($m) {
                        return (int)date('n', strtotime($w->week_end_date)) === $m;
                    })->values()->map(function($w) {
                        return ['week_number' => $w->week_number, 'label' => 'Week ' . $w->week_number];
                    })->all();
                }
            }
            $filterData[$unit->clientUnitId] = [
                'years' => $years,
                'monthsByYear' => $monthsByYear,
                'weeksByYearMonth' => $weeksByYearMonth
            ];
        }
        $financialYears = \App\Models\ClientUnitAnnualBudget::select('financial_year')->distinct()->pluck('financial_year');
        
        // Set default values for filters
        $defaultUnit = $units->first() ? $units->first()->clientUnitId : null;
        $currentFinancialYear = $this->getCurrentFinancialYear();
        
        $selectedUnit = $request->unit_id ?? $defaultUnit;
        $selectedYear = $request->financial_year ?? $currentFinancialYear;
        $selectedMonth = $request->month; // Default to null (All)
        $selectedWeek = $request->week; // Default to null (All)

        // DEBUG: Log filter values
        \Log::info('BudgetReport Filters', [
            'unit_id' => $selectedUnit,
            'financial_year' => $selectedYear,
            'month' => $selectedMonth,
            'week' => $selectedWeek,
        ]);

        // Fetch weekly budgets for the selected filters
        $weeklyBudgets = collect();
        // Always define $annualBudget, even if no row is found
        $annualBudget = null;
        $annualBudgetRow = null;
        if ($selectedUnit && $selectedYear) {
            $annualBudgetRow = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                ->where('financial_year', $selectedYear)
                ->first();
            if ($annualBudgetRow) {
                $annualBudget = $annualBudgetRow;
                $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id);
                if ($selectedMonth) {
                    $query->whereMonth('week_end_date', $selectedMonth);
                }
                if ($selectedWeek) {
                    $query->where('week_number', $selectedWeek);
                }
                $weeklyBudgets = $query->get();
            } else {
                // No annual budget for this unit/year, so no weekly budgets
                $weeklyBudgets = collect();
            }
        } else {
            $weeklyBudgets = collect();
        }

        // Pie chart data: budget vs utilisation
        $budgetTotal = 0;
        $utilisationTotal = 0;
        if ($weeklyBudgets->count() > 0) {
            $budgetTotal = $weeklyBudgets->sum('weekly_budget');
            $utilisationTotal = $weeklyBudgets->sum('total_weekly_utilisation');
        }
        $utilisation = 0;
        if (isset($weeklyBudgets) && $weeklyBudgets->count() > 0) {
            $utilisation = $weeklyBudgets->sum('total_weekly_utilisation');
        }

        // Calculate dashboard data for header cards using filtered data (not unfiltered)
        $dashboardData = $this->calculateDashboardDataFiltered($selectedUnit, $selectedYear, $selectedMonth, $selectedWeek);

        // Debug: Log the query and data for troubleshooting
        // \Log::info('Selected Unit:', [$selectedUnit]);
        // \Log::info('Selected Year:', [$selectedYear]);
        // \Log::info('Weekly Budgets:', $weeklyBudgets->toArray());
        // \Log::info('Budget Total:', [$budgetTotal]);
        // \Log::info('Utilisation Total:', [$utilisationTotal]);
        // If no data, show a warning in the view
        $noDataWarning = null;
        if ($selectedUnit && $selectedYear && $weeklyBudgets->count() === 0) {
            $noDataWarning = 'No weekly budget data found for the selected unit and year.';
        }

        return view('clientPortal.budgetView', [
            'units' => $units,
            'financialYears' => $financialYears,
            'weeklyBudgets' => $weeklyBudgets,
            'annualBudget' => $annualBudget,
            'utilisation' => $utilisation,
            'selectedUnit' => $selectedUnit,
            'selectedYear' => $selectedYear,
            'selectedMonth' => $selectedMonth,
            'selectedWeek' => $selectedWeek,
            'filterData' => $filterData,
            'budgetTotal' => $budgetTotal,
            'utilisationTotal' => $utilisationTotal,
            'noDataWarning' => $noDataWarning,
            'dashboardData' => $dashboardData,
        ]);
    }

    private function calculateDashboardDataFiltered($selectedUnit, $selectedYear, $selectedMonth = null, $selectedWeek = null)
    {
        try {
            // Get annual budget for the selected unit and year
            $annualBudget = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                ->where('financial_year', $selectedYear)
                ->first();

            if (!$annualBudget) {
                return [
                    'budget' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                    'expense' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                    'balance' => 0,
                    'expense_percentage' => 0,
                ];
            }

            // Get weekly budgets with filters applied
            $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id);
            
            if ($selectedMonth) {
                $query->whereMonth('week_end_date', $selectedMonth);
            }
            
            if ($selectedWeek) {
                $query->where('week_number', $selectedWeek);
            }
            
            $filteredWeeklyBudgets = $query->get();

            if ($filteredWeeklyBudgets->count() === 0) {
                return [
                    'budget' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                    'expense' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                    'balance' => 0,
                    'expense_percentage' => 0,
                ];
            }

            // Calculate totals from filtered weekly budgets
            $budget = $this->ensureNumeric($filteredWeeklyBudgets->sum('weekly_budget'));
            $expense = $this->ensureNumeric($filteredWeeklyBudgets->sum('total_weekly_utilisation'));
            $balance = $budget - $expense;
            $expensePercentage = $budget > 0 ? ($expense / $budget) * 100 : 0;

            // Calculate trend data by comparing with previous period
            $prevPeriodData = $this->getPreviousPeriodDataForFiltered($selectedUnit, $selectedYear, $selectedMonth, $selectedWeek);
            
            $budgetTrend = $this->calculateTrend($budget, $prevPeriodData['budget']);
            $expenseTrend = $this->calculateTrend($expense, $prevPeriodData['expense']);

            return [
                'budget' => [
                    'total' => $budget,
                    'trend' => $budgetTrend['direction'],
                    'percentage' => $budgetTrend['percentage']
                ],
                'expense' => [
                    'total' => $expense,
                    'trend' => $expenseTrend['direction'],
                    'percentage' => $expenseTrend['percentage']
                ],
                'balance' => $balance,
                'expense_percentage' => round($expensePercentage, 2),
            ];
        } catch (\Exception $e) {
            \Log::error('Error calculating filtered dashboard data: ' . $e->getMessage());
            return [
                'budget' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                'expense' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                'balance' => 0,
                'expense_percentage' => 0,
            ];
        }
    }

    private function getPreviousPeriodDataForFiltered($selectedUnit, $selectedYear, $selectedMonth, $selectedWeek)
    {
        try {
            $annualBudget = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                ->where('financial_year', $selectedYear)
                ->first();

            if (!$annualBudget) {
                return ['budget' => 0, 'expense' => 0];
            }

            $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id);

            if ($selectedWeek && $selectedMonth) {
                // Compare with previous week in the same month
                $prevWeek = $selectedWeek - 1;
                if ($prevWeek < 1) {
                    // If week 1, get last week of previous month
                    $prevMonth = $selectedMonth - 1;
                    if ($prevMonth < 1) {
                        $prevMonth = 12;
                    }
                    $query->whereMonth('week_end_date', $prevMonth);
                    // Get the highest week number for that month
                    $maxWeek = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id)
                        ->whereMonth('week_end_date', $prevMonth)
                        ->max('week_number');
                    if ($maxWeek) {
                        $query->where('week_number', $maxWeek);
                    }
                } else {
                    $query->whereMonth('week_end_date', $selectedMonth)
                          ->where('week_number', $prevWeek);
                }
            } elseif ($selectedMonth) {
                // Compare with previous month
                $prevMonth = $selectedMonth - 1;
                if ($prevMonth < 1) {
                    $prevMonth = 12;
                }
                $query->whereMonth('week_end_date', $prevMonth);
            } else {
                // Compare with previous year
                $prevYear = $selectedYear - 1;
                $prevAnnualBudget = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                    ->where('financial_year', $prevYear)
                    ->first();
                
                if (!$prevAnnualBudget) {
                    return ['budget' => 0, 'expense' => 0];
                }
                
                $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $prevAnnualBudget->id);
            }

            $prevData = $query->get();
            
            return [
                'budget' => $this->ensureNumeric($prevData->sum('weekly_budget')),
                'expense' => $this->ensureNumeric($prevData->sum('total_weekly_utilisation'))
            ];
        } catch (\Exception $e) {
            \Log::error('Error getting previous period data for filtered: ' . $e->getMessage());
            return ['budget' => 0, 'expense' => 0];
        }
    }

    private function calculateDashboardData($weeklyBudgets, $selectedUnit, $selectedYear, $selectedMonth, $selectedWeek)
    {
        if ($weeklyBudgets->count() === 0) {
            return [
                'budget' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                'expense' => ['total' => 0, 'trend' => 'positive', 'percentage' => 0],
                'balance' => 0,
                'expense_percentage' => 0,
            ];
        }

        // Ensure numeric values with proper validation - use weekly_budget for consistency
        $budget = $this->ensureNumeric($weeklyBudgets->sum('weekly_budget'));
        $expense = $this->ensureNumeric($weeklyBudgets->sum('total_weekly_utilisation'));
        $balance = $budget - $expense;
        $expensePercentage = $budget > 0 ? ($expense / $budget) * 100 : 0;

        // Calculate trend data by comparing with previous period
        $prevPeriodData = $this->getPreviousPeriodData($selectedUnit, $selectedYear, $selectedMonth, $selectedWeek);
        
        $budgetTrend = $this->calculateTrend($budget, $prevPeriodData['budget']);
        $expenseTrend = $this->calculateTrend($expense, $prevPeriodData['expense']);

        return [
            'budget' => [
                'total' => $budget,
                'trend' => $budgetTrend['direction'],
                'percentage' => $budgetTrend['percentage']
            ],
            'expense' => [
                'total' => $expense,
                'trend' => $expenseTrend['direction'],
                'percentage' => $expenseTrend['percentage']
            ],
            'balance' => $balance,
            'expense_percentage' => round($expensePercentage, 2),
        ];
    }

    private function ensureNumeric($value)
    {
        if (is_numeric($value)) {
            return (float) $value;
        }
        return 0.0;
    }

    private function getPreviousPeriodData($selectedUnit, $selectedYear, $selectedMonth, $selectedWeek)
    {
        try {
            // Get previous period data for comparison
            $annualBudget = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                ->where('financial_year', $selectedYear)
                ->first();

            if (!$annualBudget) {
                return ['budget' => 0, 'expense' => 0];
            }

            $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id);

            if ($selectedWeek && $selectedMonth) {
                // Compare with previous week
                $prevWeek = $selectedWeek - 1;
                if ($prevWeek < 1) {
                    // If week 1, get last week of previous month or return 0
                    return ['budget' => 0, 'expense' => 0];
                }
                $query->whereMonth('week_end_date', $selectedMonth)
                      ->where('week_number', $prevWeek);
            } elseif ($selectedMonth) {
                // Compare with previous month
                $prevMonth = $selectedMonth - 1;
                if ($prevMonth < 1) {
                    $prevMonth = 12;
                }
                $query->whereMonth('week_end_date', $prevMonth);
            } else {
                // Compare with previous year data (simplified)
                $prevYear = $selectedYear - 1;
                $prevAnnualBudget = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                    ->where('financial_year', $prevYear)
                    ->first();
                
                if (!$prevAnnualBudget) {
                    return ['budget' => 0, 'expense' => 0];
                }
                
                $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $prevAnnualBudget->id);
            }

            $prevData = $query->get();
            
            return [
                'budget' => $this->ensureNumeric($prevData->sum('weekly_budget')),
                'expense' => $this->ensureNumeric($prevData->sum('total_weekly_utilisation'))
            ];
        } catch (\Exception $e) {
            \Log::error('Error getting previous period data: ' . $e->getMessage());
            return ['budget' => 0, 'expense' => 0];
        }
    }

    private function calculateTrend($current, $previous)
    {
        try {
            $current = $this->ensureNumeric($current);
            $previous = $this->ensureNumeric($previous);
            
            if ($previous == 0) {
                return ['direction' => 'positive', 'percentage' => 0];
            }

            $change = $current - $previous;
            $percentage = abs(($change / $previous) * 100);
            $direction = $change >= 0 ? 'positive' : 'negative';

            return [
                'direction' => $direction,
                'percentage' => round($percentage, 2)
            ];
        } catch (\Exception $e) {
            \Log::error('Error calculating trend: ' . $e->getMessage());
            return ['direction' => 'positive', 'percentage' => 0];
        }
    }

    private function getCurrentFinancialYear()
    {
        $now = Carbon::now();
        $year = $now->year;

        // If current date is before April, we're in the previous financial year
        if ($now->month < 4) {
            $year--;
        }

        return $year;
    }

    // Add more methods as needed for AJAX/chart data
}
