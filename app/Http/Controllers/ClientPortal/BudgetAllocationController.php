<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BudgetAllocationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        // Check if the authenticated user is a client
        $this->middleware(function ($request, $next) {
            if (Auth::user()->userable_type !== Client::class) {
                abort(403, 'Unauthorized action. This section is for clients only.');
            }
            return $next($request);
        });
    }
    public function index(Request $request)
    {
        // Get authenticated client ID
        $clientId = Auth::user()->portal_id;

        // Determine financial year
        $selectedYear = $request->input('financial_year', $this->getCurrentFinancialYear());
        $financialYearStart = $selectedYear . '-04-01';
        $financialYearEnd = ($selectedYear + 1) . '-03-31';

        // Get all units belonging to the client
        $units = ClientUnit::where('clientId', $clientId)
            ->where('status', 1)
            ->get();

        // Get budget allocations for these units
        $budgetAllocations = ClientUnitAnnualBudget::where('financial_year', $selectedYear . '-' . ($selectedYear + 1))
            ->whereIn('client_unit_id', $units->pluck('clientUnitId'))
            ->get()
            ->keyBy('client_unit_id');

        // Get available financial years (last 3 years, current year, and next year)

        $financialYears = $this->getAvailableFinancialYears();

        return view('clientPortal.budgetAllocation', compact('units', 'budgetAllocations', 'selectedYear', 'financialYears'));
    }
    /**
     * Get available financial years (last 3 years, current year, and next year)
     *
     * @return array
     */
    private function getAvailableFinancialYears()
    {
        $currentFinancialYear = $this->getCurrentFinancialYear();
        $financialYears = [];
        for ($i = -3; $i <= 1; $i++) {
            $year = $currentFinancialYear + $i;
            $financialYears[$year] = $year . '-' . ($year + 1);
        }
        return $financialYears;
    }

    private function getCurrentFinancialYear()
    {
        $now = Carbon::now();
        $year = $now->year;

        // If current date is before April, we're in the previous financial year
        if ($now->month < 4) {
            $year--;
        }

        return $year;
    }

    public function add(Request $request)
    {
        $unitId = $request->route('unit_id');
        $financialYear = $request->route('financial_year', $this->getCurrentFinancialYear());

//        $financialYear = $request->input('financial_year', $this->getCurrentFinancialYear());

        // Get unit details
        $unit = ClientUnit::findOrFail($unitId);

        // Check if unit belongs to authenticated client
        if ($unit->clientId != Auth::user()->portal_id) {
            abort(403, 'Unauthorized action.');
        }

        // Calculate financial year dates
        $financialYearStart = Carbon::createFromDate($financialYear, 4, 1);
        $financialYearEnd = Carbon::createFromDate($financialYear + 1, 3, 31);
        $financialYears = $this->getAvailableFinancialYears();

        // Calculate weeks in the financial year
        $weeks = $this->calculateWeeksInFinancialYear($financialYearStart, $financialYearEnd);

        return view('clientPortal.budgetForm', compact('unit', 'financialYear', 'financialYears', 'financialYearEnd', 'weeks'));
    }

    public function edit($unitId, $financialYear)
    {
        // Get the unit with client relationship
        $unit = ClientUnit::with('client')->where('clientUnitId', $unitId)->firstOrFail();

        // Check if unit belongs to authenticated client
        if ($unit->clientId != Auth::user()->portal_id) {
            abort(403, 'Unauthorized action.');
        }

        // Get the annual budget for this unit and financial year
        $annualBudget = ClientUnitAnnualBudget::where('client_unit_id', $unitId)
            ->where('financial_year', $financialYear . '-' . ($financialYear + 1))
            ->first();

        // Calculate financial year dates
        $financialYearStart = Carbon::createFromDate($financialYear, 4, 1);
        $financialYearEnd = Carbon::createFromDate($financialYear + 1, 3, 31);

        // Calculate weeks in the financial year
        $weeks = $this->calculateWeeksInFinancialYear($financialYearStart, $financialYearEnd);
        $financialYears = $this->getAvailableFinancialYears();

        if(!$annualBudget){
            return view('clientPortal.budgetForm', compact('unit', 'financialYear', 'financialYears', 'financialYearEnd', 'weeks'));
        }

        // Get weekly budgets
        $weeklyBudgets = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id)
            ->orderBy('week_number')
            ->get()
            ->keyBy('week_number');

        // If no weekly budgets exist, use the calculated weeks
        if ($weeklyBudgets->isEmpty()) {
            $weeks = $weeks;
        } else {
            $weeks = $weeklyBudgets;
        }

        return view('clientPortal.budgetForm', compact('unit', 'financialYear', 'financialYears', 'financialYearStart', 'financialYearEnd', 'annualBudget', 'weeks', 'weeklyBudgets'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'unit_id' => 'required|exists:client_units,clientUnitId',
            'financial_year' => 'required|integer',
            'annual_budget' => 'required|numeric|min:0',
            'week_number' => 'required|integer|min:1',
            'week_start_date' => 'required|date',
            'week_end_date' => 'required|date|after_or_equal:week_start_date',
            'weekly_budget' => 'required|numeric|min:0',
            'weekly_allowance' => 'required|numeric|min:0',
            'special_allowance' => 'nullable|numeric|min:0',
        ]);

        $unitId = $request->input('unit_id');
        $financialYear = $request->input('financial_year');

        // Check if unit belongs to authenticated client
        $unit = ClientUnit::findOrFail($unitId);
        if ($unit->clientId != Auth::user()->portal_id) {
            abort(403, 'Unauthorized action.');
        }

        // Create or update annual budget
        $annualBudget = ClientUnitAnnualBudget::updateOrCreate(
            [
                'client_unit_id' => $unitId,
                'financial_year' => $financialYear . '-' . ($financialYear + 1),
            ],
            [
                'financial_year_start' => $financialYear . '-04-01',
                'financial_year_end' => ($financialYear + 1) . '-03-31',
                'annual_budget' => $request->input('annual_budget'),
                'created_by' => Auth::user()->id,
            ]
        );

        // Create or update weekly budget
        $weekNumber = $request->input('week_number');
        $weeklyBudget = $request->input('weekly_budget');
        $weeklyAllowance = $request->input('weekly_allowance');
        $specialAllowance = $request->input('special_allowance', 0);
        $totalWeeklyAllocation = $weeklyBudget + $weeklyAllowance + $specialAllowance;

        ClientUnitWeeklyBudget::updateOrCreate(
            [
                'client_unit_annual_budget_id' => $annualBudget->id,
                'week_number' => $weekNumber,
            ],
            [
                'week_start_date' => $request->input('week_start_date'),
                'week_end_date' => $request->input('week_end_date'),
                'weekly_budget' => $weeklyBudget,
                'weekly_allowance' => $weeklyAllowance,
                'special_allowance' => $specialAllowance,
                'total_weekly_allocation' => $totalWeeklyAllocation,
                'created_by' => Auth::user()->id,
            ]
        );

        return redirect()->route('budget.allocation.edit', ['unit_id' => $unitId, 'financial_year' => $financialYear])
            ->with('success', 'Budget saved successfully.');
    }

    private function calculateWeeksInFinancialYear($startDate, $endDate)
    {
        $weeks = [];
        $currentDate = clone $startDate;
        $weekNumber = 1;

        while ($currentDate <= $endDate) {
            $weekStartDate = clone $currentDate;
            $weekEndDate = (clone $currentDate)->addDays(6);

            // Ensure week end date doesn't exceed financial year end
            if ($weekEndDate > $endDate) {
                $weekEndDate = clone $endDate;
            }

            $weeks[$weekNumber] = [
                'week_number' => $weekNumber,
                'start_date' => $weekStartDate->format('Y-m-d'),
                'end_date' => $weekEndDate->format('Y-m-d'),
            ];

            $currentDate->addDays(7);
            $weekNumber++;
        }

        return $weeks;
    }

    public function generateWeeks(Request $request)
    {
        try {
            // Sanitize and validate input
            $unitId = $request->input('unit_id');
            $financialYear = $request->input('financial_year');
            $firstEndDateInput = $request->input('first_end_date');
            $weekInterval = (int) $request->input('week_interval');
            $numberOfWeeks = (int) $request->input('number_of_weeks');
            $annualBudget = (float) $request->input('annual_budget');
            $weeklyAllowancePercentage = (float) $request->input('weekly_allowance_percentage', 0);
            $specialAllowance = (float) $request->input('special_allowance', 0);

            // Validate inputs
            if (!$firstEndDateInput || !$weekInterval || !$numberOfWeeks || !$annualBudget) {
                return response()->json(['success' => false, 'message' => 'Invalid input data.'], 422);
            }

            // Check if weekly budgets already exist for this unit and financial year
            $annualBudgetRecord = ClientUnitAnnualBudget::where('client_unit_id', $unitId)
                ->where('financial_year', $financialYear)
                ->first();

            if ($annualBudgetRecord) {
                $existingWeeklyBudgets = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudgetRecord->id)
                    ->count();

                if ($existingWeeklyBudgets > 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Weekly budgets already exist for this unit and financial year.'
                    ], 422);
                }
            }

            // Calculate weekly budget (evenly distributed)
            $weeklyBudget = round($annualBudget / $numberOfWeeks, 2);

            // Calculate weekly allowance based on percentage of weekly budget
            $weeklyAllowance = round(($weeklyBudget * $weeklyAllowancePercentage) / 100, 2);

            $weeks = [];
            $consolidatedUnutilisedFund = 0;

            for ($i = 0; $i < $numberOfWeeks; $i++) {
                // Calculate week end date
                $weekEnd = $i === 0
                    ? Carbon::parse($firstEndDateInput)
                    : Carbon::parse($firstEndDateInput)->addDays($i * $weekInterval);

                // Calculate week start date (end date - interval + 1)
                $weekStart = (clone $weekEnd)->subDays($weekInterval - 1);

                $totalWeeklyAllocation = $weeklyBudget + $weeklyAllowance + $specialAllowance;

                // No utilisation initially
                $totalWeeklyUtilisation = 0;
                $percentUtilisation = 0;
                $weeklyUnutilisedFund = $totalWeeklyAllocation;

                // For week 1, cumulative balance is the same as weekly balance
                // For subsequent weeks, it's previous week's cumulative balance + current week's balance
                if ($i === 0) {
                    $consolidatedUnutilisedFund = $weeklyUnutilisedFund;
                } else {
                    $consolidatedUnutilisedFund += $weeklyUnutilisedFund;
                }

                $internalTransfers = 0;
                $balanceFund = $consolidatedUnutilisedFund + $internalTransfers;

                // Calculate new columns
                $weeklyBudgetBalance = $weeklyBudget - $totalWeeklyUtilisation;
                $weeklyBudgetUtilisationPercentage = $weeklyBudget > 0 
                    ? ($totalWeeklyUtilisation / $weeklyBudget) * 100 
                    : 0;

                $weeks[$i + 1] = [
                    'week_number' => $i + 1,
                    'start_date' => $weekStart->format('Y-m-d'),
                    'end_date' => $weekEnd->format('Y-m-d'),
                    'weekly_budget' => $weeklyBudget,
                    'weekly_allowance' => $weeklyAllowance,
                    'special_allowance' => $specialAllowance,
                    'total_weekly_allocation' => $totalWeeklyAllocation,
                    'total_weekly_utilisation' => $totalWeeklyUtilisation,
                    'percent_utilisation' => $percentUtilisation,
                    'weekly_unutilised_fund' => $weeklyUnutilisedFund,
                    'consolidated_unutilised_fund' => $consolidatedUnutilisedFund,
                    'internal_transfers' => $internalTransfers,
                    'balance_fund' => $balanceFund,
                    'weekly_budget_balance' => $weeklyBudgetBalance,
                    'weekly_budget_utilisation_percentage' => $weeklyBudgetUtilisationPercentage,
                    'notes' => ''
                ];
            }

            return response()->json([
                'success' => true,
                'weeks' => $weeks
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Server error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function saveGeneratedWeeks(Request $request)
    {
        $request->validate([
            'unit_id' => 'required|exists:client_units,clientUnitId',
            'financial_year' => 'required|integer',
            'annual_budget' => 'required|numeric|min:0',
            'weeks' => 'required|array',
            'weeks.*.week_number' => 'required|integer',
            'weeks.*.start_date' => 'required|date',
            'weeks.*.end_date' => 'required|date',
            'weeks.*.weekly_budget' => 'required|numeric|min:0',
            'weeks.*.weekly_allowance' => 'required|numeric|min:0',
            'weeks.*.special_allowance' => 'required|numeric|min:0',
        ]);

        $unitId = $request->input('unit_id');
        $financialYear = $request->input('financial_year');
        $annualBudgetAmount = $request->input('annual_budget');
        $weeks = $request->input('weeks');

        // Check if unit belongs to authenticated client
        $unit = ClientUnit::where('clientUnitId', $unitId)->firstOrFail();
        if ($unit->clientId != Auth::user()->portal_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if annual budget already exists
        $existingAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $unitId)
            ->where('financial_year', $financialYear . '-' . ($financialYear + 1))
            ->first();

        // Check if weekly budgets already exist
        if ($existingAnnualBudget) {
            $existingWeeklyBudgets = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $existingAnnualBudget->id)->count();
            if ($existingWeeklyBudgets > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Weekly budgets already exist for this unit and financial year.'
                ], 422);
            }
        }

        try {
            // Start a database transaction
            DB::beginTransaction();

            // Create or update annual budget with financial_year_start and financial_year_end
            $annualBudget = ClientUnitAnnualBudget::updateOrCreate(
                [
                    'client_unit_id' => $unitId,
                    'financial_year' => $financialYear . '-' . ($financialYear + 1),
                ],
                [
                    'financial_year_start' => $financialYear . '-04-01',
                    'financial_year_end' => ($financialYear + 1) . '-03-31',
                    'annual_budget' => $annualBudgetAmount,
                    'created_by' => Auth::user()->id,
                ]
            );

            // Save all weekly budgets
            foreach ($weeks as $week) {
                // Skip incomplete or blank week entries
                if (
                    empty($week['week_number']) ||
                    empty($week['start_date']) ||
                    empty($week['end_date']) ||
                    !isset($week['weekly_budget']) ||
                    !isset($week['weekly_allowance']) ||
                    !isset($week['special_allowance'])
                ) {
                    continue;
                }

                // Validate dates
                try {
                    $startDate = Carbon::parse($week['start_date']);
                    $endDate = Carbon::parse($week['end_date']);
                } catch (\Exception $e) {
                    // Log the error and skip this week
                    \Log::error('Invalid date format in week ' . $week['week_number'] . ': ' . $e->getMessage());
                    continue;
                }

                $totalWeeklyAllocation = $week['weekly_budget'] + $week['weekly_allowance'] + $week['special_allowance'];
                $totalWeeklyUtilisation = $week['total_weekly_utilisation'] ?? 0;
                $percentUtilisation = $totalWeeklyAllocation > 0
                    ? ($totalWeeklyUtilisation / $totalWeeklyAllocation) * 100
                    : 0;
                $weeklyBalance = $totalWeeklyAllocation - $totalWeeklyUtilisation;

                // Calculate new columns
                $weeklyBudgetBalance = $week['weekly_budget'] - $totalWeeklyUtilisation;
                $weeklyBudgetUtilisationPercentage = $week['weekly_budget'] > 0 
                    ? ($totalWeeklyUtilisation / $week['weekly_budget']) * 100 
                    : 0;

                ClientUnitWeeklyBudget::create([
                    'client_unit_annual_budget_id' => $annualBudget->id,
                    'week_number' => $week['week_number'],
                    'week_start_date' => $startDate,
                    'week_end_date' => $endDate,
                    'weekly_budget' => $week['weekly_budget'],
                    'weekly_allowance' => $week['weekly_allowance'],
                    'special_allowance' => $week['special_allowance'],
                    'total_weekly_allocation' => $totalWeeklyAllocation,
                    'total_weekly_utilisation' => $totalWeeklyUtilisation,
                    'percent_utilisation' => $percentUtilisation,
                    'weekly_unutilised_fund' => $weeklyBalance,
                    'internal_transfers' => $week['internal_transfers'] ?? 0,
                    'consolidated_unutilised_fund' => $week['consolidated_unutilised_fund'] ?? 0,
                    'balance_fund' => $week['balance_fund'] ?? 0,
                    'weekly_budget_balance' => $weeklyBudgetBalance,
                    'weekly_budget_utilisation_percentage' => $weeklyBudgetUtilisationPercentage,
                    'notes' => $week['notes'] ?? '',
                    'created_by' => Auth::user()->id,
                ]);
            }

            // Recalculate consolidated funds for all weeks
            $this->recalculateConsolidatedFunds($annualBudget->id, 1);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Weekly budgets auto-generated and saved successfully.'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error saving weekly budgets: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error saving weekly budgets: ' . $e->getMessage()
            ], 500);
        }
    }

    public function updateWeeklyBudget(Request $request)
    {
        try {
            $request->validate([
                'id' => 'required|exists:client_unit_weekly_budgets,id',
                'week_start_date' => 'required|date',
                'week_end_date' => 'required|date|after_or_equal:week_start_date',
                'weekly_budget' => 'required|numeric|min:0',
                'weekly_allowance' => 'required|numeric|min:0',
                'special_allowance' => 'nullable|numeric|min:0',
                'total_weekly_allocation' => 'nullable|numeric|min:0',
                'total_weekly_utilisation' => 'nullable|numeric|min:0',
                'internal_transfers' => 'nullable|numeric',
                'notes' => 'nullable|string|max:500',
            ]);

            $weeklyBudget = ClientUnitWeeklyBudget::findOrFail($request->id);

            // Check if unit belongs to authenticated client
            $annualBudget = $weeklyBudget->annualBudget;
            $unit = ClientUnit::findOrFail($annualBudget->client_unit_id);

            if ($unit->clientId != Auth::user()->portal_id) {
                abort(403, 'Unauthorized action.');
            }

            // Update basic fields
            $weeklyBudget->week_start_date = $request->week_start_date;
            $weeklyBudget->week_end_date = $request->week_end_date;
            $weeklyBudget->weekly_budget = $request->weekly_budget;
            $weeklyBudget->weekly_allowance = $request->weekly_allowance;
            $weeklyBudget->special_allowance = $request->special_allowance ?? 0;

            // Internal transfers can be directly set
            $weeklyBudget->internal_transfers = $request->internal_transfers ?? 0;

            // Calculate total allocation including internal transfers
            $weeklyBudget->total_weekly_allocation = $request->weekly_budget + $request->weekly_allowance + ($request->special_allowance ?? 0) + $weeklyBudget->internal_transfers;
            $weeklyBudget->total_weekly_utilisation = $request->total_weekly_utilisation ?? 0;

            // Calculate derived fields
            $weeklyBudget->percent_utilisation = $weeklyBudget->total_weekly_allocation > 0
                ? ($weeklyBudget->total_weekly_utilisation / $weeklyBudget->total_weekly_allocation) * 100
                : 0;

            $weeklyBudget->weekly_unutilised_fund = $weeklyBudget->total_weekly_allocation - $weeklyBudget->total_weekly_utilisation;

            // Calculate weekly budget balance: weekly_budget - total_weekly_utilisation
            $weeklyBudget->weekly_budget_balance = $weeklyBudget->weekly_budget - $weeklyBudget->total_weekly_utilisation;

            // Calculate weekly budget utilisation percentage: (total_weekly_utilisation / weekly_budget) * 100
            // Avoid division by zero
            if ($weeklyBudget->weekly_budget > 0) {
                $weeklyBudget->weekly_budget_utilisation_percentage = ($weeklyBudget->total_weekly_utilisation / $weeklyBudget->weekly_budget) * 100;
            } else {
                $weeklyBudget->weekly_budget_utilisation_percentage = 0;
            }

            // Notes
            $weeklyBudget->notes = $request->notes;

            // Save changes
            $weeklyBudget->save();

            // Recalculate consolidated funds and balance funds for all weeks
            // This needs to be done for all weeks after the current one
            $this->recalculateConsolidatedFunds($weeklyBudget->client_unit_annual_budget_id, $weeklyBudget->week_number);

            return response()->json([
                'success' => true,
                'message' => 'Weekly budget updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating weekly budget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a single field of a weekly budget
     */
    public function updateWeeklyBudgetField(Request $request)
    {
        try {
            $request->validate([
                'id' => 'required|exists:client_unit_weekly_budgets,id',
                'field' => 'required|string|in:special_allowance,notes',
                'value' => 'required',
            ]);

            $weeklyBudget = ClientUnitWeeklyBudget::findOrFail($request->id);

            // Check if unit belongs to authenticated client
            $annualBudget = $weeklyBudget->annualBudget;
            $unit = ClientUnit::findOrFail($annualBudget->client_unit_id);

            if ($unit->clientId != Auth::user()->portal_id) {
                abort(403, 'Unauthorized action.');
            }

            // Update the field
            if ($request->field === 'special_allowance') {
                $value = max(0, (float) $request->value);
                $weeklyBudget->special_allowance = $value;

                // Recalculate dependent fields
                $weeklyBudget->total_weekly_allocation =
                    $weeklyBudget->weekly_budget +
                    $weeklyBudget->weekly_allowance +
                    $weeklyBudget->special_allowance +
                    $weeklyBudget->internal_transfers;

                $weeklyBudget->weekly_unutilised_fund =
                    $weeklyBudget->total_weekly_allocation -
                    $weeklyBudget->total_weekly_utilisation;

                $weeklyBudget->percent_utilisation = $weeklyBudget->total_weekly_allocation > 0
                    ? ($weeklyBudget->total_weekly_utilisation / $weeklyBudget->total_weekly_allocation) * 100
                    : 0;
            } else {
                $weeklyBudget->{$request->field} = $request->value;
            }

            $weeklyBudget->save();

            // Recalculate consolidated funds for this and all subsequent weeks
            $this->recalculateConsolidatedFunds($weeklyBudget->client_unit_annual_budget_id, $weeklyBudget->week_number);

            return response()->json([
                'success' => true,
                'weeklyBudget' => $weeklyBudget
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating field: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get units available for internal transfers
     */
    public function getTransferUnits(Request $request)
    {
        try {
            $request->validate([
                'unit_id' => 'required|exists:client_units,clientUnitId',
                'week_number' => 'required|integer|min:1',
                'financial_year' => 'required',
                'transfer_type' => 'nullable|in:from,to'
            ]);

            $unitId = $request->input('unit_id');
            $weekNumber = $request->input('week_number');
            $financialYear = $request->input('financial_year');
            $transferType = $request->input('transfer_type', 'from');

            // Get authenticated client ID
            $clientId = Auth::user()->portal_id;

            // Get all units belonging to the client except the current one
            $units = ClientUnit::where('clientId', $clientId)
                ->where('status', 1)
                ->where('clientUnitId', '!=', $unitId)
                ->get();

            $unitsWithBalance = [];

            foreach ($units as $unit) {
                // Get annual budget for this unit
                $annualBudget = ClientUnitAnnualBudget::where('client_unit_id', $unit->clientUnitId)
                    ->where('financial_year', $financialYear . '-' . ($financialYear + 1))
                    ->first();

                if (!$annualBudget) {
                    continue;
                }

                // Get weekly budget for this unit and week
                $weeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id)
                    ->where('week_number', $weekNumber)
                    ->first();

                if (!$weeklyBudget) {
                    continue;
                }

                // For 'to' transfers, check if the other unit has sufficient balance
                if ($transferType === 'to' && $weeklyBudget->balance_fund <= 0) {
                    continue;
                }

                $unitsWithBalance[] = [
                    'id' => $unit->clientUnitId,
                    'name' => $unit->name,
                    'balance' => $weeklyBudget->balance_fund
                ];
            }

            return response()->json([
                'success' => true,
                'units' => $unitsWithBalance,
                'transfer_type' => $transferType
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting transfer units: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute an internal transfer between units
     */
    public function executeTransfer(Request $request)
    {
        try {
            $request->validate([
                'source_unit_id' => 'required|exists:client_units,clientUnitId',
                'target_unit_id' => 'required|exists:client_units,clientUnitId|different:source_unit_id',
                'week_number' => 'required|integer|min:1',
                'amount' => 'required|numeric|min:0.01',
                'transfer_type' => 'required|in:from,to',
                'financial_year' => 'required'
            ]);

            // Start a database transaction
            \DB::beginTransaction();

            $sourceUnitId = $request->input('source_unit_id');
            $targetUnitId = $request->input('target_unit_id');
            $weekNumber = $request->input('week_number');
            $amount = (float) $request->input('amount');
            $financialYear = $request->input('financial_year');

            // Check if source unit belongs to authenticated client
            $sourceUnit = ClientUnit::findOrFail($sourceUnitId);
            if ($sourceUnit->clientId != Auth::user()->portal_id) {
                abort(403, 'Unauthorized action.');
            }

            // Check if target unit belongs to authenticated client
            $targetUnit = ClientUnit::findOrFail($targetUnitId);
            if ($targetUnit->clientId != Auth::user()->portal_id) {
                abort(403, 'Unauthorized action.');
            }

            // Get annual budgets for both units
            $sourceAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $sourceUnitId)
                ->where('financial_year', $financialYear . '-' . ($financialYear + 1))
                ->firstOrFail();

            $targetAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $targetUnitId)
                ->where('financial_year', $financialYear . '-' . ($financialYear + 1))
                ->firstOrFail();

            // Get weekly budgets for both units
            $sourceWeeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $sourceAnnualBudget->id)
                ->where('week_number', $weekNumber)
                ->firstOrFail();

            $targetWeeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $targetAnnualBudget->id)
                ->where('week_number', $weekNumber)
                ->firstOrFail();

            // Check if source unit has enough balance (only for outgoing transfers)
            // For negative balances, we allow transfers to bring them back to positive
            if ($sourceWeeklyBudget->balance_fund < $amount && $sourceWeeklyBudget->balance_fund >= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient balance for transfer. Available: £' . number_format($sourceWeeklyBudget->balance_fund, 2)
                ], 422);
            }

            // Log before transfer for debugging
            \Log::info('Before Transfer:', [
                'source_unit' => $sourceUnit->name,
                'target_unit' => $targetUnit->name,
                'amount' => $amount,
                'source_internal_transfers_before' => $sourceWeeklyBudget->internal_transfers,
                'target_internal_transfers_before' => $targetWeeklyBudget->internal_transfers,
                'source_balance_before' => $sourceWeeklyBudget->balance_fund,
                'target_balance_before' => $targetWeeklyBudget->balance_fund
            ]);

            // Update internal transfers for both units
            $sourceWeeklyBudget->internal_transfers -= $amount;
            $targetWeeklyBudget->internal_transfers += $amount;

            // Recalculate and update total weekly allocation for source unit
            $sourceWeeklyBudget->total_weekly_allocation = $sourceWeeklyBudget->weekly_budget +
                                                          $sourceWeeklyBudget->weekly_allowance +
                                                          $sourceWeeklyBudget->special_allowance +
                                                          $sourceWeeklyBudget->internal_transfers;

            // Recalculate and update total weekly allocation for target unit
            $targetWeeklyBudget->total_weekly_allocation = $targetWeeklyBudget->weekly_budget +
                                                          $targetWeeklyBudget->weekly_allowance +
                                                          $targetWeeklyBudget->special_allowance +
                                                          $targetWeeklyBudget->internal_transfers;

            // Update balance funds - Balance Fund = Cumulative Balance (which already includes internal transfers effect)
            // No need to add internal_transfers again as it's already included in the cumulative calculation
            $sourceWeeklyBudget->balance_fund = $sourceWeeklyBudget->consolidated_unutilised_fund;
            $targetWeeklyBudget->balance_fund = $targetWeeklyBudget->consolidated_unutilised_fund;

            // Save changes
            $sourceWeeklyBudget->save();
            $targetWeeklyBudget->save();

            // Recalculate cumulative balances for both units from the affected week onwards
            $this->recalculateConsolidatedFunds($sourceAnnualBudget->id, $weekNumber);
            $this->recalculateConsolidatedFunds($targetAnnualBudget->id, $weekNumber);

            // Refresh the weekly budget records to get updated values
            $sourceWeeklyBudget->refresh();
            $targetWeeklyBudget->refresh();

            // Log after transfer for debugging
            \Log::info('After Transfer:', [
                'source_internal_transfers_after' => $sourceWeeklyBudget->internal_transfers,
                'target_internal_transfers_after' => $targetWeeklyBudget->internal_transfers,
                'source_balance_after' => $sourceWeeklyBudget->balance_fund,
                'target_balance_after' => $targetWeeklyBudget->balance_fund,
                'source_total_allocation_after' => $sourceWeeklyBudget->total_weekly_allocation,
                'target_total_allocation_after' => $targetWeeklyBudget->total_weekly_allocation
            ]);

            // Log the transfer for audit purposes
            \DB::table('client_unit_budget_transfers')->insert([
                'source_unit_id' => $sourceUnitId,
                'target_unit_id' => $targetUnitId,
                'source_weekly_budget_id' => $sourceWeeklyBudget->id,
                'target_weekly_budget_id' => $targetWeeklyBudget->id,
                'amount' => $amount,
                'transferred_by' => Auth::user()->id,
                'transferred_at' => now(),
                'notes' => 'Transfer from ' . $sourceUnit->name . ' to ' . $targetUnit->name
            ]);

            // Commit the transaction
            \DB::commit();

            // Get updated balance for the current unit to return to frontend
            $currentUnitId = $request->input('transfer_type') === 'from' ? $sourceUnitId : $targetUnitId;
            $currentWeeklyBudget = $request->input('transfer_type') === 'from' ? $sourceWeeklyBudget : $targetWeeklyBudget;

            return response()->json([
                'success' => true,
                'message' => 'Transfer completed successfully',
                'updated_balance' => $currentWeeklyBudget->balance_fund,
                'updated_internal_transfers' => $currentWeeklyBudget->internal_transfers,
                'updated_total_allocation' => $currentWeeklyBudget->total_weekly_allocation
            ]);
        } catch (\Exception $e) {
            // Roll back the transaction in case of error
            \DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error executing transfer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get transfer history for a unit and week
     */
    public function getTransferHistory(Request $request)
    {
        try {
            $request->validate([
                'unit_id' => 'required|exists:client_units,clientUnitId',
                'week_number' => 'required|integer|min:1',
                'financial_year' => 'required'
            ]);

            $unitId = $request->input('unit_id');
            $weekNumber = $request->input('week_number');
            $financialYear = $request->input('financial_year');

            // Get authenticated client ID
            $clientId = Auth::user()->portal_id;

            // Verify unit belongs to authenticated client
            $unit = ClientUnit::where('clientUnitId', $unitId)
                ->where('clientId', $clientId)
                ->firstOrFail();

            // Get annual budget for this unit
            $annualBudget = ClientUnitAnnualBudget::where('client_unit_id', $unitId)
                ->where('financial_year', $financialYear . '-' . ($financialYear + 1))
                ->first();

            if (!$annualBudget) {
                return response()->json([
                    'success' => true,
                    'transfers' => []
                ]);
            }

            // Get weekly budget for this unit and week
            $weeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id)
                ->where('week_number', $weekNumber)
                ->first();

            if (!$weeklyBudget) {
                return response()->json([
                    'success' => true,
                    'transfers' => []
                ]);
            }

            // Get transfer history for this weekly budget
            $transfers = \DB::table('client_unit_budget_transfers as t')
                ->leftJoin('client_units as source_unit', 't.source_unit_id', '=', 'source_unit.clientUnitId')
                ->leftJoin('client_units as target_unit', 't.target_unit_id', '=', 'target_unit.clientUnitId')
                ->where(function($query) use ($weeklyBudget) {
                    $query->where('t.source_weekly_budget_id', $weeklyBudget->id)
                          ->orWhere('t.target_weekly_budget_id', $weeklyBudget->id);
                })
                ->select([
                    't.amount',
                    't.transferred_at',
                    't.notes',
                    't.source_unit_id',
                    't.target_unit_id',
                    'source_unit.name as source_unit_name',
                    'target_unit.name as target_unit_name'
                ])
                ->orderBy('t.transferred_at', 'desc')
                ->get();

            $transferHistory = [];
            foreach ($transfers as $transfer) {
                if ($transfer->source_unit_id == $unitId) {
                    // Outgoing transfer
                    $transferHistory[] = [
                        'direction' => 'outgoing',
                        'other_unit_name' => $transfer->target_unit_name,
                        'amount' => $transfer->amount,
                        'transferred_at' => $transfer->transferred_at,
                        'notes' => $transfer->notes
                    ];
                } else {
                    // Incoming transfer
                    $transferHistory[] = [
                        'direction' => 'incoming',
                        'other_unit_name' => $transfer->source_unit_name,
                        'amount' => $transfer->amount,
                        'transferred_at' => $transfer->transferred_at,
                        'notes' => $transfer->notes
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'transfers' => $transferHistory
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting transfer history: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Recalculate consolidated unutilised funds and balance funds for all weeks
     * starting from the specified week number
     */
    private function recalculateConsolidatedFunds($annualBudgetId, $startWeekNumber)
    {
        // Get all weeks for this annual budget in order
        $weeks = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudgetId)
            ->orderBy('week_number', 'asc')
            ->get();

        $consolidatedFund = 0;

        foreach ($weeks as $week) {
            // Skip weeks before the start week
            if ($week->week_number < $startWeekNumber) {
                $consolidatedFund = $week->consolidated_unutilised_fund;
                continue;
            }

            // Recalculate total weekly allocation to include internal transfers
            $week->total_weekly_allocation = $week->weekly_budget + $week->weekly_allowance + $week->special_allowance + $week->internal_transfers;

            // Calculate weekly unutilised fund (Weekly Balance)
            $weeklyUnutilisedFund = $week->total_weekly_allocation - $week->total_weekly_utilisation;
            $week->weekly_unutilised_fund = $weeklyUnutilisedFund;

            // Calculate Cumulative Balance:
            // Week 1: Cumulative Balance = Week 1's Weekly Balance
            // Week n: Cumulative Balance = Week n's Weekly Balance + Week (n-1)'s Cumulative Balance
            if ($week->week_number == 1) {
                $consolidatedFund = $weeklyUnutilisedFund;
            } else {
                $consolidatedFund += $weeklyUnutilisedFund;
            }
            $week->consolidated_unutilised_fund = $consolidatedFund;

            // Update balance fund - Balance Fund = Cumulative Balance (which already includes internal transfers effect)
            // The cumulative balance already accounts for internal transfers through total_weekly_allocation
            $week->balance_fund = $consolidatedFund;

            // Calculate weekly budget balance: weekly_budget - total_weekly_utilisation
            $week->weekly_budget_balance = $week->weekly_budget - $week->total_weekly_utilisation;

            // Calculate weekly budget utilisation percentage: (total_weekly_utilisation / weekly_budget) * 100
            // Avoid division by zero
            if ($week->weekly_budget > 0) {
                $week->weekly_budget_utilisation_percentage = ($week->total_weekly_utilisation / $week->weekly_budget) * 100;
            } else {
                $week->weekly_budget_utilisation_percentage = 0;
            }

            // Save changes
            $week->save();
        }
    }

    public function storeWeeklyBudgets(Request $request)
    {
        try {
            $request->validate([
                'unit_id' => 'required|exists:client_units,clientUnitId',
                'financial_year' => 'required|string',
                'annual_budget' => 'required|numeric|min:0',
                'weeks' => 'required|array',
                'weeks.*.week_number' => 'required|integer|min:1',
                'weeks.*.start_date' => 'required|date',
                'weeks.*.end_date' => 'required|date|after_or_equal:weeks.*.start_date',
                'weeks.*.weekly_budget' => 'required|numeric|min:0',
                'weeks.*.weekly_allowance' => 'nullable|numeric|min:0',
                'weeks.*.special_allowance' => 'nullable|numeric|min:0',
            ]);

            // Start a database transaction
            \DB::beginTransaction();

            // Create or update the annual budget record
            $annualBudget = ClientUnitAnnualBudget::updateOrCreate(
                [
                    'client_unit_id' => $request->unit_id,
                    'financial_year' => $request->financial_year
                ],
                [
                    'annual_budget' => $request->annual_budget,
                    'financial_year_start' => substr($request->financial_year, 0, 4) . '-04-01',
                    'financial_year_end' => (substr($request->financial_year, 0, 4) + 1) . '-03-31',
                    'created_by' => Auth::user()->id,
                ]
            );

            // Process each weekly budget
            foreach ($request->weeks as $weekData) {
                $totalAllocation = $weekData['weekly_budget'] +
                                   ($weekData['weekly_allowance'] ?? 0) +
                                   ($weekData['special_allowance'] ?? 0) +
                                   ($weekData['internal_transfers'] ?? 0);

                $totalUtilisation = $weekData['total_weekly_utilisation'] ?? 0;
                $percentUtilisation = $totalAllocation > 0 ? ($totalUtilisation / $totalAllocation) * 100 : 0;
                $weeklyUnutilised = $totalAllocation - $totalUtilisation;

                // Calculate new columns
                $weeklyBudgetBalance = $weekData['weekly_budget'] - $totalUtilisation;
                $weeklyBudgetUtilisationPercentage = $weekData['weekly_budget'] > 0 
                    ? ($totalUtilisation / $weekData['weekly_budget']) * 100 
                    : 0;

                ClientUnitWeeklyBudget::updateOrCreate(
                    [
                        'client_unit_annual_budget_id' => $annualBudget->id,
                        'week_number' => $weekData['week_number']
                    ],
                    [
                        'week_start_date' => $weekData['start_date'],
                        'week_end_date' => $weekData['end_date'],
                        'weekly_budget' => $weekData['weekly_budget'],
                        'weekly_allowance' => $weekData['weekly_allowance'] ?? 0,
                        'special_allowance' => $weekData['special_allowance'] ?? 0,
                        'total_weekly_allocation' => $totalAllocation,
                        'total_weekly_utilisation' => $totalUtilisation,
                        'percent_utilisation' => $percentUtilisation,
                        'weekly_unutilised_fund' => $weeklyUnutilised,
                        'internal_transfers' => $weekData['internal_transfers'] ?? 0,
                        'consolidated_unutilised_fund' => $weekData['consolidated_unutilised_fund'] ?? 0,
                        'balance_fund' => $weekData['balance_fund'] ?? 0,
                        'weekly_budget_balance' => $weeklyBudgetBalance,
                        'weekly_budget_utilisation_percentage' => $weeklyBudgetUtilisationPercentage,
                        'notes' => $weekData['notes'] ?? '',
                        'created_by' => Auth::user()->id,
                    ]
                );
            }

            // Recalculate consolidated funds for all weeks
            $this->recalculateConsolidatedFunds($annualBudget->id, 1);

            // Commit the transaction
            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Weekly budgets saved successfully'
            ]);

        } catch (\Exception $e) {
            // Roll back the transaction in case of error
            \DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error saving weekly budgets: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate the start date of a week based on its end date and the week interval
     *
     * @param string|Carbon $endDate The end date of the week
     * @param int $weekInterval The number of days in a week
     * @return Carbon The start date of the week
     */
    private function calculateWeekStartDate($endDate, $weekInterval)
    {
        if (!$endDate instanceof Carbon) {
            $endDate = Carbon::parse($endDate);
        }

        return (clone $endDate)->subDays($weekInterval - 1);
    }

    /**
     * Calculate the end date of a week based on its start date and the week interval
     *
     * @param string|Carbon $startDate The start date of the week
     * @param int $weekInterval The number of days in a week
     * @return Carbon The end date of the week
     */
    private function calculateWeekEndDate($startDate, $weekInterval)
    {
        if (!$startDate instanceof Carbon) {
            $startDate = Carbon::parse($startDate);
        }

        return (clone $startDate)->addDays($weekInterval - 1);
    }

    /**
     * Update multiple weekly budgets after recalculation
     */
    public function updateRecalculated(Request $request)
    {
        $request->validate([
            'rows' => 'required|array',
            'rows.*.id' => 'required|exists:client_unit_weekly_budgets,id',
            'rows.*.weekly_budget' => 'required|numeric|min:0',
            'rows.*.weekly_allowance' => 'required|numeric|min:0',
            'rows.*.special_allowance' => 'required|numeric|min:0',
            'rows.*.total_weekly_allocation' => 'required|numeric|min:0',
            'rows.*.total_weekly_utilisation' => 'required|numeric|min:0',
            'rows.*.percent_utilisation' => 'required|numeric|min:0',
            'rows.*.weekly_unutilised_fund' => 'required|numeric',
            'rows.*.internal_transfers' => 'required|numeric',
            'rows.*.notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $annualBudgetId = null;
            $startWeekNumber = PHP_INT_MAX;

            foreach ($request->rows as $rowData) {
                $weeklyBudget = ClientUnitWeeklyBudget::findOrFail($rowData['id']);

                // Store annual budget ID for recalculation
                if (!$annualBudgetId) {
                    $annualBudgetId = $weeklyBudget->client_unit_annual_budget_id;
                }

                // Track the earliest week number for recalculation
                $startWeekNumber = min($startWeekNumber, $weeklyBudget->week_number);

                // Update the weekly budget
                $weeklyBudget->update([
                    'weekly_budget' => $rowData['weekly_budget'],
                    'weekly_allowance' => $rowData['weekly_allowance'],
                    'special_allowance' => $rowData['special_allowance'],
                    'total_weekly_allocation' => $rowData['total_weekly_allocation'],
                    'total_weekly_utilisation' => $rowData['total_weekly_utilisation'],
                    'percent_utilisation' => $rowData['percent_utilisation'],
                    'weekly_unutilised_fund' => $rowData['weekly_unutilised_fund'],
                    'weekly_budget_balance' => $rowData['weekly_budget'] - $rowData['total_weekly_utilisation'],
                    'weekly_budget_utilisation_percentage' => $rowData['weekly_budget'] > 0 
                        ? ($rowData['total_weekly_utilisation'] / $rowData['weekly_budget']) * 100 
                        : 0,
                    'internal_transfers' => $rowData['internal_transfers'],
                    'notes' => $rowData['notes'],
                ]);
            }

            // Recalculate consolidated funds for all weeks starting from the earliest modified week
            if ($annualBudgetId && $startWeekNumber < PHP_INT_MAX) {
                $this->recalculateConsolidatedFunds($annualBudgetId, $startWeekNumber);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Weekly budgets updated successfully.'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error updating weekly budgets: ' . $e->getMessage()
            ], 500);
        }
    }
}
