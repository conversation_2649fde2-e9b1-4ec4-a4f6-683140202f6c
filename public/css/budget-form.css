/**
 * Budget Form Custom Styles
 * Additional styling for the budget form components
 */

/* Budget Summary Card - Proper Styling */

/* Override any conflicting styles from other CSS files */
.marginCenter.GpBoxAuto.shadowWidget#budgetSummaryCard,
.content-wrapper .marginCenter.GpBoxAuto.shadowWidget#budgetSummaryCard,
#budgetSummaryCard.marginCenter.GpBoxAuto.shadowWidget {
    background: linear-gradient(135deg, #f2f7f2 0%, #e8f0e8 100%) !important;
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 20px 40px rgba(242, 247, 242, 0.3) !important;
    overflow: hidden !important;
    position: relative !important;
    margin-top: 20px !important;
    padding: 0 !important; /* Override any padding from GpBoxAuto */
}

/* Week Ending column styling - removed red color, keeping normal styling */
#weeklyBudgetsTable .date-cell {
    font-weight: normal;
}


/* Week Ending header styling - removed red color */
#weeklyBudgetsTable .week-ending-header {
    font-weight: bold;
}

/* Modified row styling for recalculation */
.modified-row {
    background-color: #fffde7 !important;
}

.modified-row td {
    font-weight: bold;
}

/* Readonly input styling */
input[readonly] {
    background-color: #f8f9fa !important;
    cursor: not-allowed;
}

/* Transfer button styling */
.transfer-btn {
    font-size: 13px;
    padding: 2px 6px;
}

/* Budget table responsive styling */
.budgetTble {
    font-size: 14px;
}

/* Enhanced form validation styling */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* Calculation preview styling */
#calculation-preview {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Enhanced button styling for data attributes */
[data-action] {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

[data-action]:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Loading state for buttons */
[data-action].loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Enhanced table cell styling */
[data-cell-type] {
    position: relative;
}

[data-editable="true"] {
    cursor: pointer;
    border-bottom: 1px dashed #ccc;
}

[data-editable="true"]:hover {
    background-color: #f8f9fa;
}

/* Checkbox styling */
[data-checkbox-type] {
    cursor: pointer;
}

/* Transfer button enhancements */
[data-action="transfer"], [data-action="transfer-history"] {
    margin: 1px;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .budgetTble {
        font-size: 13px;
    }

    [data-action] {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

.budgetTble th,
.budgetTble td {
    vertical-align: middle;
    padding: 8px;
}

/* Loading overlay styling */
#budget-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: white;
    font-size: 18px;
}

/* Alert styling improvements - High specificity to override other styles */
#budget-alerts-container .alert,
.content .alert,
div.alert {
    margin-bottom: 1rem !important;
    color: #212529 !important; /* Bootstrap default dark text */
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    padding: 12px 16px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    text-shadow: none !important;
}

/* Success alerts - green with high specificity */
#budget-alerts-container .alert-success,
.content .alert-success,
div.alert-success {
    color: #155724 !important;
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
}

/* Error/Danger alerts - red with high specificity */
#budget-alerts-container .alert-danger,
.content .alert-danger,
div.alert-danger {
    color: #721c24 !important;
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}

/* Warning alerts - yellow/orange with high specificity */
#budget-alerts-container .alert-warning,
.content .alert-warning,
div.alert-warning {
    color: #856404 !important;
    background-color: #fff3cd !important;
    border-color: #ffeaa7 !important;
}

/* Info alerts - blue with high specificity */
#budget-alerts-container .alert-info,
.content .alert-info,
div.alert-info {
    color: #0c5460 !important;
    background-color: #d1ecf1 !important;
    border-color: #bee5eb !important;
}

/* Primary alerts - blue with high specificity */
#budget-alerts-container .alert-primary,
.content .alert-primary,
div.alert-primary {
    color: #004085 !important;
    background-color: #cce7ff !important;
    border-color: #b3d7ff !important;
}

/* Secondary alerts - gray with high specificity */
#budget-alerts-container .alert-secondary,
.content .alert-secondary,
div.alert-secondary {
    color: #383d41 !important;
    background-color: #e2e3e5 !important;
    border-color: #d6d8db !important;
}

/* Alert close button styling with high specificity */
#budget-alerts-container .alert .close,
.content .alert .close,
div.alert .close {
    color: #000 !important;
    opacity: 0.7 !important;
    font-size: 20px !important;
    font-weight: bold !important;
    line-height: 1 !important;
    text-shadow: none !important;
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

#budget-alerts-container .alert .close:hover,
.content .alert .close:hover,
div.alert .close:hover {
    opacity: 1 !important;
    color: #000 !important;
    text-decoration: none !important;
}

/* Override any potential white text from other stylesheets */
.alert * {
    color: inherit !important;
}

/* Specific overrides for common conflicting styles */
.alert,
.alert p,
.alert span,
.alert div {
    text-shadow: none !important;
}

/* Force dark text on light backgrounds with maximum specificity */
.alert-success,
.alert-success *,
.alert-success span,
.alert-success p,
.alert-success div {
    color: #155724 !important;
}

.alert-danger,
.alert-danger *,
.alert-danger span,
.alert-danger p,
.alert-danger div {
    color: #721c24 !important;
}

.alert-warning,
.alert-warning *,
.alert-warning span,
.alert-warning p,
.alert-warning div {
    color: #856404 !important;
}

.alert-info,
.alert-info *,
.alert-info span,
.alert-info p,
.alert-info div {
    color: #0c5460 !important;
}

.alert-primary,
.alert-primary *,
.alert-primary span,
.alert-primary p,
.alert-primary div {
    color: #004085 !important;
}

.alert-secondary,
.alert-secondary *,
.alert-secondary span,
.alert-secondary p,
.alert-secondary div {
    color: #383d41 !important;
}

/* Nuclear option - override any possible conflicting styles */
[id^="alert-"] {
    color: #212529 !important;
}

[id^="alert-"].alert-success {
    color: #155724 !important;
}

[id^="alert-"].alert-danger {
    color: #721c24 !important;
}

[id^="alert-"].alert-warning {
    color: #856404 !important;
}

[id^="alert-"].alert-info {
    color: #0c5460 !important;
}

[id^="alert-"].alert-primary {
    color: #004085 !important;
}

[id^="alert-"].alert-secondary {
    color: #383d41 !important;
}

/* Budget alerts container - prevent white space when empty */
#budget-alerts-container:empty {
    display: none !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 0 !important;
}

#budget-alerts-container {
    margin: 0;
    padding: 0;
}

/* Transfer modal styling - Override conflicting modal.css */
#transferModal {
    z-index: 1050 !important;
    display: none !important; /* Override modal.css */
}

#transferModal.show {
    display: block !important;
}

#transferModal .modal-backdrop {
    z-index: 1040 !important;
}

#transferModal .modal-dialog {
    z-index: 1060 !important;
    margin: 30px auto !important;
}

#transferModal .modal-content {
    background-color: #fff !important;
    border: 1px solid rgba(0,0,0,.2) !important;
    border-radius: 6px !important;
    box-shadow: 0 3px 9px rgba(0,0,0,.5) !important;
}

#transferModal .modal-header {
    background-color: #f5f5f5 !important;
    border-bottom: 1px solid #e5e5e5 !important;
    padding: 15px !important;
}

#transferModal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
    padding: 15px !important;
}

#transferModal .modal-footer {
    background-color: #f5f5f5 !important;
    border-top: 1px solid #e5e5e5 !important;
    padding: 15px !important;
}

#transferModal .transfer-amount {
    width: 120px;
}

#transferModal .execute-transfer-btn {
    min-width: 80px;
}

#transferModal .close {
    color: #000 !important;
    opacity: 0.7 !important;
    font-size: 20px !important;
    font-weight: bold !important;
    background: none !important;
    border: none !important;
}

#transferModal .close:hover {
    opacity: 1 !important;
}

/* Transfer button styling */
.transfer-btn, .transfer-history-btn {
    font-size: 13px;
    padding: 2px 6px;
    margin: 1px;
}

.transfer-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.transfer-history-btn:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.transfer-buttons {
    display: flex;
    justify-content: center;
    gap: 2px;
}

/* Transfer type selection styling */
.form-check-label {
    cursor: pointer;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s;
}

.form-check-input:checked + .form-check-label {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.form-check-label:hover {
    background-color: #f8f9fa;
}

.form-check-input:checked + .form-check-label:hover {
    background-color: #0056b3;
}

/* Transfer history modal styling */
#transferHistoryModal .table-success th {
    background-color: #d4edda;
    color: #155724;
}

#transferHistoryModal .table-danger th {
    background-color: #f8d7da;
    color: #721c24;
}

#transferHistoryModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Transfer direction indicators */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Transfer summary styling */
.bg-light {
    background-color: #f8f9fa !important;
}

/* Transfer session info styling */
.transfer-session-info {
    display: flex;
    align-items: center;
}

.transfer-session-info small {
    font-size: 0.875rem;
    color: #6c757d;
}

#transferCount {
    font-weight: bold;
    color: #007bff;
}

/* Modal footer enhancements */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#doneTransferModal {
    font-weight: bold;
}

#doneTransferModal:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Form section styling */
.GpBoxAuto {
    margin-bottom: 2rem;
}

.BudTitle {
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

/* Button group styling */
.recalculate-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Checkbox styling */
.row-checkbox,
#selectAllCheckbox {
    transform: scale(1.2);
    margin: 0;
}

/* Edit mode styling */
.edit-mode input,
.edit-mode textarea {
    border: 2px solid #007bff;
}

/* Calculation input highlighting */
.calculation-input:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Preview section styling */
#previewSection {
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 20px;
    background-color: #f8f9ff;
}

/* Auto-generate section styling */
#autoGenerateSection {
    background-color: #f8f9fa;
}

/* Transfer modal styling */
#transferModal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
}

/* Preview table container for better responsiveness */
#previewSection .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

/* Preview table specific styling for consistency */
#previewTable {
    table-layout: fixed !important;
    width: 100% !important;
    min-width: 1200px; /* Minimum width to prevent cramping */
    margin-bottom: 0 !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
}

#previewTable th,
#previewTable td {
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    border: 1px solid #dee2e6 !important;
    padding: 8px 4px !important;
    vertical-align: middle !important;
    white-space: nowrap;
    box-sizing: border-box !important;
}

/* Specific border handling to prevent double borders */
#previewTable th:not(:first-child),
#previewTable td:not(:first-child) {
    border-left: none !important;
}

#previewTable tr:not(:first-child) th,
#previewTable tr:not(:first-child) td {
    border-top: none !important;
}

/* Fixed column widths for preview table */
#previewTable th:nth-child(1), #previewTable td:nth-child(1) { width: 8% !important; min-width: 60px; } /* Week */
#previewTable th:nth-child(2), #previewTable td:nth-child(2) { width: 9% !important; min-width: 80px; } /* Week Ending */
#previewTable th:nth-child(3), #previewTable td:nth-child(3) { width: 8.5% !important; min-width: 75px; } /* Weekly Budget */
#previewTable th:nth-child(4), #previewTable td:nth-child(4) { width: 8.5% !important; min-width: 75px; } /* Weekly Allowance */
#previewTable th:nth-child(5), #previewTable td:nth-child(5) { width: 8.5% !important; min-width: 75px; } /* Special Allowance */
#previewTable th:nth-child(6), #previewTable td:nth-child(6) { width: 9% !important; min-width: 85px; } /* Total Allowance */
#previewTable th:nth-child(7), #previewTable td:nth-child(7) { width: 8.5% !important; min-width: 75px; } /* Total Utilisation */
#previewTable th:nth-child(8), #previewTable td:nth-child(8) { width: 8.5% !important; min-width: 75px; } /* Budget Balance */
#previewTable th:nth-child(9), #previewTable td:nth-child(9) { width: 8.5% !important; min-width: 75px; } /* Budget Utilisation % */
#previewTable th:nth-child(10), #previewTable td:nth-child(10) { width: 8.5% !important; min-width: 75px; } /* Weekly Balance */
#previewTable th:nth-child(11), #previewTable td:nth-child(11) { width: 8% !important; min-width: 70px; } /* % Utilisation */
#previewTable th:nth-child(12), #previewTable td:nth-child(12) { width: 9.5% !important; min-width: 90px; } /* Consolidated Fund */

#previewTable th[data-column],
#weeklyBudgetsTable th[data-column] {
    text-align: center;
    font-weight: bold;
}

/* Numerical columns should be right-aligned */
#previewTable th[data-column="weekly-budget"],
#previewTable th[data-column="weekly-allowance"], 
#previewTable th[data-column="special-allowance"],
#previewTable th[data-column="total-allocation"],
#previewTable th[data-column="total-utilisation"],
#previewTable th[data-column="calculated-weekly-budget-balance"],
#previewTable th[data-column="weekly-budget-utilisation-percent"],
#previewTable th[data-column="weekly-balance"],
#previewTable th[data-column="percent-utilisation"],
#previewTable th[data-column="cumulative-balance"] {
    text-align: right;
}

/* Ensure preview table body cells match main table alignment */
#previewTable tbody td:nth-child(n+3) {
    text-align: right;
}

/* Week ending column should be center aligned */
#previewTable .week-ending-header,
#previewTable tbody td:nth-child(2) {
    text-align: center;
}

/* Week number column */
#previewTable tbody td:nth-child(1) {
    text-align: center;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .budgetTble {
        font-size: 13px;
    }
    
    .budgetTble th,
    .budgetTble td {
        padding: 4px;
    }
    
    .transfer-btn {
        font-size: 10px;
        padding: 1px 4px;
    }
}

/* ================================================================
   BUDGET SUMMARY CARD STYLES - Futuristic Design with High Specificity
   ================================================================ */

.content-wrapper #budgetSummaryCard,
#budgetSummaryCard {
    background: linear-gradient(135deg, #f2f7f2 0%, #e8f0e8 100%) !important;
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(242, 247, 242, 0.25) !important;
    overflow: hidden !important;
    position: relative !important;
    margin-top: 15px !important;
    margin-bottom: 15px !important;
}

.content-wrapper #budgetSummaryCard::before,
#budgetSummaryCard::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%) !important;
    pointer-events: none !important;
}

.content-wrapper #budgetSummaryCard .budget-summary-header,
#budgetSummaryCard .budget-summary-header {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
}

.content-wrapper #budgetSummaryCard .budget-summary-header .BudTitle,
#budgetSummaryCard .budget-summary-header .BudTitle {
    color: #4a4a4a !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    display: flex !important;
    padding-right: 60px !important;
    align-items: center !important;
    gap: 12px !important;
}

.content-wrapper #budgetSummaryCard .summary-icon,
#budgetSummaryCard .summary-icon {
    font-size: 28px !important;
    color: #ffd700 !important;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5) !important;
    animation: pulse-glow 2s ease-in-out infinite alternate !important;
}

@keyframes pulse-glow {
    from { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    to { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
}

.content-wrapper #budgetSummaryCard .financial-year-badge,
#budgetSummaryCard .financial-year-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52) !important;
    color: white !important;
    padding: 6px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-left: auto !important;
    box-shadow: 0 4px 15px rgba(238, 90, 82, 0.4) !important;
}

.content-wrapper #budgetSummaryCard .budget-summary-content,
#budgetSummaryCard .budget-summary-content {
    padding: 16px !important;
    position: relative !important;
}

.content-wrapper #budgetSummaryCard .budget-summary-content .row,
#budgetSummaryCard .budget-summary-content .row {
    display: flex !important;
    align-items: stretch !important;
}

.content-wrapper #budgetSummaryCard .summary-section,
#budgetSummaryCard .summary-section {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 8px !important;
    padding: 14px !important;
    height: 100% !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

.summary-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f2f7f2, #e8f0e8);
}

.summary-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.summary-section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f3f4;
}

.section-icon {
    font-size: 20px;
    color: #6b8e6b;
    background: linear-gradient(135deg, #f2f7f2, #e8f0e8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

/* Annual Budget Section Styles */
.annual-budget-section .summary-value-container {
    text-align: center;
    margin: 20px 0;
}

.summary-main-value {
    font-size: 32px;
    font-weight: 700;
    color: #27ae60;
    text-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
    line-height: 1.2;
    margin-bottom: 6px;
}

.summary-sub-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.summary-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #ddd, transparent);
    margin: 20px 0;
}

.summary-details .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.summary-details .detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
}

.detail-value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
}

/* Generation Configuration Section */
.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 8px;
    margin: 12px 0;
}

.config-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.config-item:hover {
    background: #e3f2fd;
    border-color: #6b8e6b;
    transform: scale(1.02);
}

.config-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
}

.config-icon {
    font-size: 12px;
    color: #6b8e6b;
}

.config-value {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
}

/* Prevent automatic number formatting for date fields */
.config-value[data-no-format="true"] {
    white-space: nowrap;
}

/* Weekly Breakdown Section - Grid Layout like Generation Details */
.breakdown-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 8px !important;
    margin: 12px 0 !important;
}

.breakdown-item {
    background: rgba(242, 247, 242, 0.5) !important;
    border-radius: 6px !important;
    padding: 10px !important;
    text-align: center !important;
    border: 1px solid rgba(242, 247, 242, 0.8) !important;
    transition: all 0.3s ease !important;
}

.breakdown-item:hover {
    background: rgba(242, 247, 242, 0.7) !important;
    transform: translateY(-2px) !important;
}

.breakdown-label {
    font-size: 11px !important;
    color: #666 !important;
    margin-bottom: 6px !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.breakdown-value {
    font-size: 14px !important;
    font-weight: 700 !important;
    color: #2c3e50 !important;
}

.breakdown-value.highlighted {
    color: #e74c3c !important;
    font-size: 15px !important;
}

.breakdown-divider-mini {
    display: none !important; /* Remove dividers in grid layout */
}

.utilization-status {
    text-align: center;
    padding: 12px;
    border-radius: 8px;
    margin-top: 12px;
}

.utilization-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.utilization-indicator.status-good {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4);
}

.utilization-indicator.status-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
}

.utilization-indicator.status-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

/* Utilization Progress Bar Animation */
.utilization-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: utilization-shine 2s ease-in-out infinite;
}

@keyframes utilization-shine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.utilization-icon {
    animation: utilization-pulse 2s ease-in-out infinite;
}

@keyframes utilization-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.utilization-icon {
    font-size: 16px;
    animation: rotate-slow 4s linear infinite;
}

@keyframes rotate-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design for Summary Card */
@media (max-width: 992px) {
    .budget-summary-content .row {
        margin: 0;
    }
    
    .budget-summary-content .col-md-4 {
        margin-bottom: 15px;
        padding: 0 8px;
    }
    
    .summary-main-value {
        font-size: 28px;
    }
    
    .config-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr 1fr;
        gap: 8px;
    }

    .breakdown-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .breakdown-value {
        font-size: 13px;
    }

    .breakdown-value.highlighted {
        font-size: 14px;
    }

    .summary-section {
        padding: 15px !important;
    }
}

@media (max-width: 768px) {
    .budget-summary-header {
        padding: 15px 20px;
    }
    
    .budget-summary-header .BudTitle {
        font-size: 20px;
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .financial-year-badge {
        margin-left: 0;
    }
    
    .budget-summary-content {
        padding: 20px 15px;
    }
    
    .summary-section {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    .summary-main-value {
        font-size: 24px;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(5, 1fr);
        gap: 8px;
    }
    
    .config-item {
        padding: 10px;
    }
    
    .config-value {
        font-size: 14px;
    }
    
    .config-label {
        font-size: 10px;
    }
}