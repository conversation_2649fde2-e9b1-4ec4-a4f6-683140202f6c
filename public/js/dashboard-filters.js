/**
 * Dashboard Unit and Financial Year Filter Functionality
 * Handles dropdown interactions and AJAX requests for dynamic filtering
 */

class DashboardFilters {
    constructor() {
        console.log('DashboardFilters constructor called');
        this.unitDropdown = document.getElementById('unit-filter');
        this.financialYearDropdown = document.getElementById('financial-year-filter');
        this.monthDropdown = document.getElementById('month-filter');
        this.weekDropdown = document.getElementById('week-filter');
        this.filterButton = document.getElementById('apply-filters-btn');
        this.resetButton = document.getElementById('reset-filters-btn');
        
        console.log('Elements found:', {
            unit: !!this.unitDropdown,
            financialYear: !!this.financialYearDropdown,
            month: !!this.monthDropdown,
            week: !!this.weekDropdown,
            filterButton: !!this.filterButton,
            resetButton: !!this.resetButton
        });
        
        this.init();
    }

    init() {
        console.log('DashboardFilters init called');
        
        if (this.unitDropdown) {
            this.unitDropdown.addEventListener('change', this.handleUnitChange.bind(this));
            console.log('Unit dropdown listener added');
        }
        
        if (this.financialYearDropdown) {
            this.financialYearDropdown.addEventListener('change', this.handleFinancialYearChange.bind(this));
            console.log('Financial year dropdown listener added');
        }

        if (this.monthDropdown) {
            this.monthDropdown.addEventListener('change', this.handleMonthChange.bind(this));
            console.log('Month dropdown listener added');
        }

        if (this.weekDropdown) {
            this.weekDropdown.addEventListener('change', this.handleWeekChange.bind(this));
            console.log('Week dropdown listener added');
        }
        
        if (this.filterButton) {
            this.filterButton.addEventListener('click', this.applyFilters.bind(this));
            console.log('Filter button listener added successfully');
        } else {
            console.error('Filter button not found! Element with ID "apply-filters-btn" does not exist.');
        }
        
        if (this.resetButton) {
            this.resetButton.addEventListener('click', this.resetFilters.bind(this));
            console.log('Reset button listener added successfully');
        } else {
            console.error('Reset button not found! Element with ID "reset-filters-btn" does not exist.');
        }
        
        // Initialize dropdown states
        this.updateFinancialYearDropdown();
        this.updateMonthDropdown();
        this.updateWeekDropdown();
        
        // If unit is already selected on page load, ensure other dropdowns are enabled
        this.initializeFromPageState();
    }
    
    /**
     * Initialize dropdowns based on current page state (URL parameters)
     */
    initializeFromPageState() {
        const selectedUnit = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        
        if (selectedUnit && this.financialYearDropdown) {
            // If unit is selected but financial year dropdown is disabled, enable it
            if (this.financialYearDropdown.disabled && this.financialYearDropdown.options.length <= 1) {
                this.loadFinancialYears(selectedUnit);
            } else {
                this.financialYearDropdown.disabled = false;
            }
        }

        if (selectedUnit && selectedFinancialYear && this.monthDropdown) {
            // If unit and financial year are selected but month dropdown is disabled, enable it
            if (this.monthDropdown.disabled && this.monthDropdown.options.length <= 1) {
                this.loadMonths(selectedUnit, selectedFinancialYear);
            } else {
                this.monthDropdown.disabled = false;
            }
        }

        if (selectedUnit && selectedFinancialYear && this.weekDropdown) {
            // If unit and financial year are selected but week dropdown is disabled, enable it
            const selectedMonth = this.monthDropdown?.value;
            if (this.weekDropdown.disabled && this.weekDropdown.options.length <= 1) {
                this.loadWeeks(selectedUnit, selectedFinancialYear, selectedMonth);
            } else {
                this.weekDropdown.disabled = false;
            }
        }
    }

    /**
     * Handle unit selection change
     */
    async handleUnitChange() {
        const selectedUnitId = this.unitDropdown.value;
        
        console.log('Unit changed to:', selectedUnitId);
        
        if (!selectedUnitId) {
            this.resetFinancialYearDropdown();
            this.resetMonthDropdown();
            this.resetWeekDropdown();
            return;
        }

        // Load financial years for the selected unit but don't apply filters automatically
        await this.loadFinancialYears(selectedUnitId);
        this.resetMonthDropdown();
        this.resetWeekDropdown();
    }

    /**
     * Handle financial year selection change
     */
    async handleFinancialYearChange() {
        const selectedUnitId = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;

        console.log('Financial year changed to:', selectedFinancialYear);

        if (!selectedUnitId || !selectedFinancialYear) {
            this.resetMonthDropdown();
            this.resetWeekDropdown();
            return;
        }

        // Load months for the selected financial year but don't apply filters automatically
        await this.loadMonths(selectedUnitId, selectedFinancialYear);
        this.resetWeekDropdown();
    }

    /**
     * Handle month selection change
     */
    async handleMonthChange() {
        const selectedUnitId = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        const selectedMonth = this.monthDropdown?.value;

        console.log('Month changed to:', selectedMonth);

        if (!selectedUnitId || !selectedFinancialYear) {
            this.resetWeekDropdown();
            return;
        }

        // Load weeks for the selected month but don't apply filters automatically
        await this.loadWeeks(selectedUnitId, selectedFinancialYear, selectedMonth);
    }

    /**
     * Handle week selection change
     */
    handleWeekChange() {
        const selectedWeek = this.weekDropdown?.value;
        console.log('Week changed to:', selectedWeek);
        // Note: Removed automatic filtering - now only applies when Filter button is clicked
    }

    /**
     * Apply filters when the Filter button is clicked
     */
    applyFilters() {
        console.log('=== Apply filters button clicked ===');
        
        const selectedUnit = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        const selectedMonth = this.monthDropdown?.value;
        const selectedWeek = this.weekDropdown?.value;
        
        console.log('Current filter values:', {
            unit: selectedUnit,
            financialYear: selectedFinancialYear,
            month: selectedMonth,
            week: selectedWeek
        });
        
        // Validate minimum required filters
        if (!selectedUnit) {
            console.warn('Unit not selected, showing alert');
            alert('Please select a unit before applying filters.');
            return;
        }
        
        if (!selectedFinancialYear) {
            console.warn('Financial year not selected, showing alert');
            alert('Please select a financial year before applying filters.');
            return;
        }
        
        console.log('Validation passed, applying filters...');
        
        // Update button state to show loading
        const originalText = this.filterButton.innerHTML;
        this.filterButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
        this.filterButton.disabled = true;
        
        console.log('Button state updated to loading');
        
        // Apply the filters with page reload to ensure charts are updated
        this.triggerDataRefresh(true);
    }

    /**
     * Reset all filters and reload dashboard with aggregated data for all units
     */
    resetFilters() {
        console.log('=== Reset filters button clicked ===');
        
        // Update reset button state to show loading
        const originalText = this.resetButton.innerHTML;
        this.resetButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
        this.resetButton.disabled = true;
        
        console.log('Reset button state updated to loading');
        
        // Clear all dropdown selections
        if (this.unitDropdown) {
            this.unitDropdown.value = '';
        }
        if (this.financialYearDropdown) {
            this.financialYearDropdown.value = '';
            this.financialYearDropdown.disabled = true;
        }
        if (this.monthDropdown) {
            this.monthDropdown.value = '';
            this.monthDropdown.disabled = true;
        }
        if (this.weekDropdown) {
            this.weekDropdown.value = '';
            this.weekDropdown.disabled = true;
        }
        
        // Reset dropdowns to default state
        this.resetFinancialYearDropdown();
        this.resetMonthDropdown();
        this.resetWeekDropdown();
        
        console.log('All filters cleared, redirecting to dashboard...');
        
        // Redirect to dashboard without any filters to show aggregated data for all units
        window.location.href = '/dashboard';
    }

    /**
     * Load financial years for selected unit via AJAX
     */
    async loadFinancialYears(unitId) {
        try {
            this.showLoadingState(this.financialYearDropdown, 'Loading financial years...');
            
            const response = await fetch(`/dashboard/financial-years?unit_id=${unitId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            this.populateFinancialYears(data.financial_years);
            
        } catch (error) {
            console.error('Error loading financial years:', error);
            this.showErrorState(this.financialYearDropdown, 'Error loading years');
        }
    }

    /**
     * Load months for selected unit and financial year via AJAX
     */
    async loadMonths(unitId, financialYear) {
        try {
            console.log('Loading months for unit:', unitId, 'financial year:', financialYear);
            this.showLoadingState(this.monthDropdown, 'Loading months...');
            
            const response = await fetch(`/dashboard/months?unit_id=${unitId}&financial_year=${financialYear}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            console.log('Months response:', data);
            this.populateMonths(data.months);
            
        } catch (error) {
            console.error('Error loading months:', error);
            this.showErrorState(this.monthDropdown, 'Error loading months');
        }
    }

    /**
     * Load weeks for selected unit, financial year, and optionally month via AJAX
     */
    async loadWeeks(unitId, financialYear, month = null) {
        try {
            console.log('Loading weeks for unit:', unitId, 'financial year:', financialYear, 'month:', month);
            this.showLoadingState(this.weekDropdown, 'Loading weeks...');
            
            let url = `/dashboard/weeks?unit_id=${unitId}&financial_year=${financialYear}`;
            if (month) {
                url += `&month=${month}`;
            }
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            console.log('Weeks response:', data);
            this.populateWeeks(data.weeks);
            
        } catch (error) {
            console.error('Error loading weeks:', error);
            this.showErrorState(this.weekDropdown, 'Error loading weeks');
        }
    }

    /**
     * Populate financial year dropdown with fetched data
     */
    populateFinancialYears(financialYears) {
        if (!this.financialYearDropdown) return;

        // Clear existing options except the first one
        this.financialYearDropdown.innerHTML = '<option value="">Select Financial Year</option>';

        if (financialYears && financialYears.length > 0) {
            financialYears.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                const nextYearShort = String((parseInt(year) + 1) % 100).padStart(2, '0');
                option.textContent = `${year}-${nextYearShort}`;
                this.financialYearDropdown.appendChild(option);
            });

            this.financialYearDropdown.disabled = false;
            this.hideHelperMessage('financial-year-filter');
        } else {
            // No financial years available for this unit
            this.financialYearDropdown.disabled = true;
            this.updateHelperMessage('financial-year-filter', 'No financial years available for this unit');
        }
    }

    /**
     * Populate months dropdown with fetched data
     */
    populateMonths(months) {
        console.log('Populating months:', months);
        if (!this.monthDropdown) {
            console.error('Month dropdown not found');
            return;
        }

        // Clear existing options except the first one
        this.monthDropdown.innerHTML = '<option value="">Select Month</option>';

        if (months && months.length > 0) {
            months.forEach(month => {
                const option = document.createElement('option');
                option.value = month.value;
                option.textContent = month.label;
                this.monthDropdown.appendChild(option);
            });

            this.monthDropdown.disabled = false;
            this.hideHelperMessage('month-filter');
            console.log('Months populated successfully, dropdown enabled');
        } else {
            console.log('No months found, resetting dropdown');
            this.resetMonthDropdown();
        }
    }

    /**
     * Populate weeks dropdown with fetched data
     */
    populateWeeks(weeks) {
        console.log('Populating weeks:', weeks);
        if (!this.weekDropdown) {
            console.error('Week dropdown not found');
            return;
        }

        // Clear existing options except the first one
        this.weekDropdown.innerHTML = '<option value="">Select Week</option>';

        if (weeks && weeks.length > 0) {
            weeks.forEach(week => {
                const option = document.createElement('option');
                option.value = week.week_number;
                option.textContent = week.label;
                this.weekDropdown.appendChild(option);
            });

            this.weekDropdown.disabled = false;
            this.hideHelperMessage('week-filter');
            console.log('Weeks populated successfully, dropdown enabled');
        } else {
            console.log('No weeks found, resetting dropdown');
            this.resetWeekDropdown();
        }
    }

    /**
     * Populate month dropdown with fetched data
     */
    populateMonths(months) {
        if (!this.monthDropdown) return;

        // Clear existing options except the first one
        this.monthDropdown.innerHTML = '<option value="">Select Month</option>';

        if (months && months.length > 0) {
            months.forEach(month => {
                const option = document.createElement('option');
                option.value = month.value;
                option.textContent = month.label;
                this.monthDropdown.appendChild(option);
            });

            this.monthDropdown.disabled = false;
            this.hideHelperMessage('month-filter');
        } else {
            this.resetMonthDropdown();
        }
    }

    /**
     * Populate week dropdown with fetched data
     */
    populateWeeks(weeks) {
        if (!this.weekDropdown) return;

        // Clear existing options except the first one
        this.weekDropdown.innerHTML = '<option value="">Select Week</option>';

        if (weeks && weeks.length > 0) {
            weeks.forEach(week => {
                const option = document.createElement('option');
                option.value = week.week_number;
                option.textContent = week.label;
                this.weekDropdown.appendChild(option);
            });

            this.weekDropdown.disabled = false;
        } else {
            this.resetWeekDropdown();
        }
    }

    /**
     * Reset financial year dropdown to initial state
     */
    resetFinancialYearDropdown() {
        if (!this.financialYearDropdown) return;
        
        this.financialYearDropdown.innerHTML = '<option value="">Select Financial Year</option>';
        this.financialYearDropdown.disabled = true;
        this.updateHelperMessage('financial-year-filter', 'Select a specific unit to make this filter active');
    }

    /**
     * Reset month dropdown to initial state
     */
    resetMonthDropdown() {
        if (!this.monthDropdown) return;
        
        this.monthDropdown.innerHTML = '<option value="">Select Month</option>';
        this.monthDropdown.disabled = true;
        
        const selectedUnit = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        
        if (!selectedUnit) {
            this.updateHelperMessage('month-filter', 'Select a specific unit to make this filter active');
        } else if (selectedUnit && this.financialYearDropdown && this.financialYearDropdown.options.length <= 1) {
            this.updateHelperMessage('month-filter', 'No financial years available for this unit');
        } else if (!selectedFinancialYear) {
            this.updateHelperMessage('month-filter', 'Select a financial year to make this filter active');
        }
    }

    /**
     * Reset week dropdown to initial state
     */
    resetWeekDropdown() {
        if (!this.weekDropdown) return;
        
        this.weekDropdown.innerHTML = '<option value="">Select Week</option>';
        this.weekDropdown.disabled = true;
        
        const selectedUnit = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        
        if (!selectedUnit) {
            this.updateHelperMessage('week-filter', 'Select a specific unit to make this filter active');
        } else if (selectedUnit && this.financialYearDropdown && this.financialYearDropdown.options.length <= 1) {
            this.updateHelperMessage('week-filter', 'No financial years available for this unit');
        } else if (!selectedFinancialYear) {
            this.updateHelperMessage('week-filter', 'Select a financial year to make this filter active');
        }
    }

    /**
     * Update financial year dropdown based on current unit selection
     */
    updateFinancialYearDropdown() {
        const selectedUnit = this.unitDropdown?.value;
        
        if (!selectedUnit) {
            this.resetFinancialYearDropdown();
        }
    }

    /**
     * Update month dropdown based on current unit and financial year selection
     */
    updateMonthDropdown() {
        const selectedUnit = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        
        if (!selectedUnit || !selectedFinancialYear) {
            this.resetMonthDropdown();
        }
    }

    /**
     * Update week dropdown based on current unit, financial year, and month selection
     */
    updateWeekDropdown() {
        const selectedUnit = this.unitDropdown?.value;
        const selectedFinancialYear = this.financialYearDropdown?.value;
        
        if (!selectedUnit || !selectedFinancialYear) {
            this.resetWeekDropdown();
        }
    }

    /**
     * Show loading state for dropdown
     */
    showLoadingState(dropdown, message = 'Loading...') {
        if (!dropdown) return;
        
        dropdown.innerHTML = `<option value="">${message}</option>`;
        dropdown.disabled = true;
    }

    /**
     * Show error state
     */
    showErrorState(dropdown, message = 'Error loading data') {
        if (!dropdown) return;
        
        dropdown.innerHTML = `<option value="">${message}</option>`;
        dropdown.disabled = true;
    }

    /**
     * Get current filter values
     */
    getCurrentFilters() {
        return {
            unitId: this.unitDropdown?.value || null,
            financialYear: this.financialYearDropdown?.value || null,
            month: this.monthDropdown?.value || null,
            week: this.weekDropdown?.value || null
        };
    }

    /**
     * Set filter values programmatically
     */
    setFilters(unitId = null, financialYear = null, month = null, week = null) {
        if (this.unitDropdown && unitId) {
            this.unitDropdown.value = unitId;
            this.handleUnitChange();
        }

        if (this.financialYearDropdown && financialYear) {
            // Wait for unit change to complete then set financial year
            setTimeout(() => {
                this.financialYearDropdown.value = financialYear;
                this.handleFinancialYearChange();
            }, 500);
        }

        if (this.monthDropdown && month) {
            // Wait for financial year change to complete then set month
            setTimeout(() => {
                this.monthDropdown.value = month;
                this.handleMonthChange();
            }, 1000);
        }

        if (this.weekDropdown && week) {
            // Wait for month change to complete then set week
            setTimeout(() => {
                this.weekDropdown.value = week;
            }, 1500);
        }
    }

    /**
     * Trigger data refresh based on current filters
     */
    triggerDataRefresh(forcePageReload = false) {
        const filters = this.getCurrentFilters();
        
        console.log('Triggering data refresh with filters:', filters);
        
        // For AJAX updates instead of full page reload
        if (!forcePageReload) {
            return this.refreshDashboardDataOnly(filters);
        }
        
        // Show loading state on dashboard components
        this.showDashboardLoading();
        
        // Refresh the entire page with the new filters to update all data
        this.refreshPageWithFilters(filters);
        
        // Dispatch custom event for other dashboard components to listen to
        const event = new CustomEvent('dashboardFiltersChanged', {
            detail: filters
        });
        document.dispatchEvent(event);
        
        return Promise.resolve();
    }

    /**
     * Refresh dashboard data via AJAX without page reload
     */
    async refreshDashboardDataOnly(filters) {
        try {
            console.log('Refreshing dashboard data via AJAX:', filters);
            
            const params = new URLSearchParams();
            if (filters.unitId) params.append('unit_id', filters.unitId);
            if (filters.financialYear) params.append('financial_year', filters.financialYear);
            if (filters.month) params.append('month', filters.month);
            if (filters.week) params.append('week', filters.week);
            
            const response = await fetch(`/dashboard/filtered-data?${params}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            console.log('Dashboard data refreshed:', data);
            
            // Update the dashboard cards with new data
            this.updateDashboardCards(data.data);
            
            // Dispatch event with the filtered data
            const event = new CustomEvent('dashboardDataRefreshed', {
                detail: data
            });
            document.dispatchEvent(event);
            
        } catch (error) {
            console.error('Error refreshing dashboard data:', error);
            // Fall back to page reload on error
            this.triggerDataRefresh(true);
        }
    }

    /**
     * Update dashboard cards with new data
     */
    updateDashboardCards(dashboardData) {
        console.log('Updating dashboard cards with data:', dashboardData);
        
        // Update Budget card
        const budgetValue = document.querySelector('.header_cards .card .value');
        if (budgetValue && dashboardData.budget && dashboardData.budget.total !== undefined) {
            budgetValue.textContent = `£${this.formatNumber(dashboardData.budget.total)}`;
        }
        
        // Update Expense card  
        const expenseCards = document.querySelectorAll('.header_cards .card');
        if (expenseCards.length > 1 && dashboardData.expense && dashboardData.expense.total !== undefined) {
            const expenseValue = expenseCards[1].querySelector('.value');
            if (expenseValue) {
                expenseValue.textContent = `£${this.formatNumber(dashboardData.expense.total)}`;
            }
        }
        
        // Update Balance card
        if (expenseCards.length > 2 && dashboardData.balance !== undefined) {
            const balanceValue = expenseCards[2].querySelector('.value');
            const balanceChange = expenseCards[2].querySelector('.change');
            if (balanceValue) {
                balanceValue.textContent = `£${this.formatNumber(dashboardData.balance)}`;
            }
            if (balanceChange) {
                const isPositive = dashboardData.balance >= 0;
                balanceChange.className = `change ${isPositive ? 'positive' : 'negative'}`;
                balanceChange.innerHTML = `${isPositive ? '↑' : '↓'} Weekly Budget - Utilisation`;
            }
        }
        
        // Update Expense Percentage card
        if (expenseCards.length > 3 && dashboardData.expense_percentage !== undefined) {
            const expensePercentValue = expenseCards[3].querySelector('.value');
            if (expensePercentValue) {
                expensePercentValue.textContent = `${this.formatNumber(dashboardData.expense_percentage)}%`;
            }
        }
    }

    /**
     * Format number for display
     */
    formatNumber(num) {
        if (typeof num !== 'number') return '0.00';
        return num.toFixed(2);
    }

    /**
     * Refresh page with current filter values
     */
    refreshPageWithFilters(filters) {
        const url = new URL(window.location.href);
        
        // Clear existing filter parameters
        url.searchParams.delete('unit_id');
        url.searchParams.delete('financial_year');
        url.searchParams.delete('month');
        url.searchParams.delete('week');
        
        // Add current filter values
        if (filters.unitId) {
            url.searchParams.set('unit_id', filters.unitId);
        }
        if (filters.financialYear) {
            url.searchParams.set('financial_year', filters.financialYear);
        }
        if (filters.month) {
            url.searchParams.set('month', filters.month);
        }
        if (filters.week) {
            url.searchParams.set('week', filters.week);
        }
        
        // Redirect to the updated URL
        window.location.href = url.toString();
    }

    /**
     * Show loading state on dashboard components
     */
    showDashboardLoading() {
        // Add loading overlay to main content areas
        const dashboardCards = document.querySelectorAll('.header_cards .card, .booking_card_grid .card');
        dashboardCards.forEach(card => {
            card.style.opacity = '0.6';
            card.style.pointerEvents = 'none';
        });
        
        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.id = 'dashboard-loading';
        loadingMessage.innerHTML = `
            <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                        background: rgba(255,255,255,0.9); padding: 20px; border-radius: 8px; 
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 9999;">
                <div style="text-align: center;">
                    <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; 
                                border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <div style="margin-top: 10px;">Updating dashboard...</div>
                </div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        document.body.appendChild(loadingMessage);
    }

    /**
     * Refresh dashboard data via AJAX (optional)
     */
    async refreshDashboardData(filters) {
        try {
            const params = new URLSearchParams();
            if (filters.unitId) params.append('unit_id', filters.unitId);
            if (filters.financialYear) params.append('financial_year', filters.financialYear);
            if (filters.month) params.append('month', filters.month);
            if (filters.week) params.append('week', filters.week);
            
            const response = await fetch(`/dashboard/filtered-data?${params}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            
            // Dispatch event with the filtered data
            const event = new CustomEvent('dashboardDataRefreshed', {
                detail: data
            });
            document.dispatchEvent(event);
            
        } catch (error) {
            console.error('Error refreshing dashboard data:', error);
        }
    }

    /**
     * Update helper message for a filter
     */
    updateHelperMessage(filterId, message) {
        const filterElement = document.getElementById(filterId);
        if (!filterElement) return;

        const formGroup = filterElement.closest('.form-group');
        if (!formGroup) return;

        // Remove existing helper message
        const existingHelper = formGroup.querySelector('.text-muted');
        if (existingHelper) {
            existingHelper.remove();
        }

        // Add new helper message
        const helperElement = document.createElement('small');
        helperElement.className = 'text-muted';
        helperElement.textContent = message;
        formGroup.appendChild(helperElement);
    }

    /**
     * Hide helper message for a filter
     */
    hideHelperMessage(filterId) {
        const filterElement = document.getElementById(filterId);
        if (!filterElement) return;

        const formGroup = filterElement.closest('.form-group');
        if (!formGroup) return;

        const existingHelper = formGroup.querySelector('.text-muted');
        if (existingHelper) {
            existingHelper.remove();
        }
    }

}

// Initialize dashboard filters when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - initializing DashboardFilters');
    
    try {
        window.dashboardFilters = new DashboardFilters();
        console.log('DashboardFilters instance created successfully');
    } catch (error) {
        console.error('Error creating DashboardFilters instance:', error);
        
        // Fallback: Simple button functionality
        console.log('Setting up fallback filter button functionality');
        const filterButton = document.getElementById('apply-filters-btn');
        
        if (filterButton) {
            console.log('Filter button found, adding fallback click handler');
            filterButton.addEventListener('click', function() {
                console.log('Fallback filter button clicked');
                
                const unit = document.getElementById('unit-filter')?.value;
                const financialYear = document.getElementById('financial-year-filter')?.value;
                
                if (!unit) {
                    alert('Please select a unit before applying filters.');
                    return;
                }
                
                if (!financialYear) {
                    alert('Please select a financial year before applying filters.');
                    return;
                }
                
                // Show loading state
                const originalText = filterButton.innerHTML;
                filterButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
                filterButton.disabled = true;
                
                // Simulate filter operation
                setTimeout(() => {
                    filterButton.innerHTML = originalText;
                    filterButton.disabled = false;
                    console.log('Fallback filter operation completed');
                    
                    // Trigger a page reload with filters (simple approach)
                    const params = new URLSearchParams(window.location.search);
                    params.set('unit_id', unit);
                    params.set('financial_year', financialYear);
                    
                    const month = document.getElementById('month-filter')?.value;
                    const week = document.getElementById('week-filter')?.value;
                    
                    if (month) params.set('month', month);
                    if (week) params.set('week', week);
                    
                    window.location.search = params.toString();
                }, 1000);
            });
        } else {
            console.error('Filter button not found in fallback mode either!');
        }
    }
});

// Export for potential external use
window.DashboardFilters = DashboardFilters;
