/**
 * Budget Recalculation Module
 * Handles bulk recalculation of budget rows
 */

window.BudgetRecalculate = {
    modifiedRows: new Set(),

    /**
     * Initialize recalculation functionality
     */
    init: function() {
        this.bindEvents();
        this.addStyles();
    },

    /**
     * Bind event handlers
     */
    bindEvents: function() {
        // Use data attributes for checkboxes and buttons
        $('[data-checkbox-type="select-all"]').on('change', this.handleSelectAll.bind(this));
        $(document).on('change', '[data-checkbox-type="week-row"]', this.handleRowCheckbox.bind(this));
        $('[data-action="recalculate"]').on('click', this.handleRecalculate.bind(this));
        $('[data-action="save-changes"]').on('click', this.handleSaveChanges.bind(this));
        $('[data-action="cancel-changes"]').on('click', this.handleCancelChanges.bind(this));
    },

    /**
     * <PERSON>le select all checkbox
     */
    handleSelectAll: function() {
        const isChecked = $('[data-checkbox-type="select-all"]').is(':checked');
        $('[data-checkbox-type="week-row"]').prop('checked', isChecked);
    },

    /**
     * Handle individual row checkbox
     */
    handleRowCheckbox: function() {
        const allChecked = $('[data-checkbox-type="week-row"]:checked').length === $('[data-checkbox-type="week-row"]').length;
        $('[data-checkbox-type="select-all"]').prop('checked', allChecked);
    },

    /**
     * Handle recalculate button click
     */
    handleRecalculate: function() {
        const selectedRows = $('[data-checkbox-type="week-row"]:checked').closest('tr');

        if (selectedRows.length === 0) {
            BudgetUtils.showRecalculateAlert('Please select at least one row to recalculate.');
            return;
        }

        this.modifiedRows.clear();
        $('[data-row-type="budget-row"]').removeClass('modified-row');

        // Sort selected rows by week number to ensure proper order
        const sortedRows = selectedRows.toArray().sort((a, b) => {
            const weekA = this.getWeekNumber($(a));
            const weekB = this.getWeekNumber($(b));
            return weekA - weekB;
        });

        // Recalculate each row in order
        sortedRows.forEach((element) => {
            this.recalculateRow($(element));
        });

        // After individual row calculations, update cumulative balances only for selected and subsequent weeks
        const firstWeekNumber = this.getWeekNumber($(sortedRows[0]));
        const selectedWeekNumbers = sortedRows.map(row => this.getWeekNumber($(row)));

        console.log('Recalculate: Selected weeks:', selectedWeekNumbers, 'Starting cumulative update from week:', firstWeekNumber);

        this.updateCumulativeBalancesForSelectedWeeks(firstWeekNumber, sortedRows);

        this.showSaveActions();
    },

    /**
     * Recalculate values for a single row
     */
    recalculateRow: function(row) {
        const rowId = row.data('id');

        // Get current values with safe parsing using data attributes
        const weeklyBudget = this.safeParseNumber(row.find('[data-cell-type="weekly-budget"]').text());
        const weeklyAllowance = this.safeParseNumber(row.find('[data-cell-type="weekly-allowance"]').text());
        const specialAllowance = this.safeParseNumber(row.find('[data-cell-type="special-allowance"]').text());
        const totalUtilisation = this.safeParseNumber(row.find('[data-cell-type="total-utilisation"]').text());
        const internalTransfers = this.safeParseNumber(row.find('[data-cell-type="internal-transfers"]').text());

        // For individual row calculations, we'll calculate basic values first
        // Cumulative balance will be recalculated properly at the end
        const cumulativeBalance = this.safeParseNumber(row.find('[data-cell-type="cumulative-balance"]').text());

        // Debug logging
        console.log('Recalculate input values:', {
            weeklyBudget,
            weeklyAllowance,
            specialAllowance,
            totalUtilisation,
            cumulativeBalance,
            internalTransfers
        });

        // Calculate new values
        const calculations = this.performCalculations({
            weeklyBudget,
            weeklyAllowance,
            specialAllowance,
            totalUtilisation,
            cumulativeBalance,
            internalTransfers
        });

        console.log('Recalculate output values:', calculations);

        // Update cells with new values
        this.updateRowCells(row, calculations);

        // Mark row as modified
        row.addClass('modified-row');
        this.modifiedRows.add(rowId);

        // Note: Cumulative balance recalculation is now handled at the end of handleRecalculate()
        // to avoid multiple recalculations when processing multiple rows
    },

    /**
     * Get week number from a table row
     */
    getWeekNumber: function(row) {
        const originalData = JSON.parse(row.attr('data-original-values') || '{}');
        return originalData.week_number || 0;
    },

    /**
     * Update cumulative balances only for selected weeks and subsequent weeks
     * This prevents corrupting unselected earlier weeks
     */
    updateCumulativeBalancesForSelectedWeeks: function(firstWeekNumber, selectedRows) {
        const tableRows = $('#weeklyBudgetsTable tbody tr[data-original-values]');
        const allWeeks = [];

        // Extract data from ALL rows to get complete picture
        tableRows.each(function() {
            const row = $(this);
            const originalData = JSON.parse(row.attr('data-original-values') || '{}');

            if (originalData.week_number) {
                const weekData = {
                    week_number: originalData.week_number,
                    weekly_budget: BudgetUtils.parseCurrency(row.find('[data-cell-type="weekly-budget"], .weekly-budget-cell').text()),
                    weekly_allowance: BudgetUtils.parseCurrency(row.find('[data-cell-type="weekly-allowance"], .weekly-allowance-cell').text()),
                    special_allowance: BudgetUtils.parseCurrency(row.find('[data-cell-type="special-allowance"], .special-allowance-cell').text()),
                    internal_transfers: BudgetUtils.parseCurrency(row.find('[data-cell-type="internal-transfers"], .internal-transfers-cell').text()),
                    total_weekly_utilisation: BudgetUtils.parseCurrency(row.find('[data-cell-type="total-utilisation"], .total-utilisation-cell').text()),
                    consolidated_unutilised_fund: BudgetUtils.parseCurrency(row.find('[data-cell-type="cumulative-balance"], .cumulative-balance-cell').text())
                };
                allWeeks.push(weekData);
            }
        });

        // Recalculate cumulative balances starting from the first selected week
        const updatedWeeks = BudgetUtils.recalculateCumulativeBalances(allWeeks, firstWeekNumber);

        // Get set of selected week numbers for efficient lookup
        const selectedWeekNumbers = new Set();
        selectedRows.forEach(row => {
            selectedWeekNumbers.add(this.getWeekNumber($(row)));
        });

        // Only update rows that are selected OR come after the first selected week
        console.log('Updating cumulative balances for weeks >= ', firstWeekNumber);

        updatedWeeks.forEach(week => {
            // Only update if this week is selected OR comes after the first selected week
            if (week.week_number >= firstWeekNumber) {
                console.log('Updating week', week.week_number, 'with cumulative balance:', week.consolidated_unutilised_fund);
                // Find the row for this week
                let targetRow = null;
                tableRows.each(function() {
                    const originalData = JSON.parse($(this).attr('data-original-values') || '{}');
                    if (originalData.week_number == week.week_number) {
                        targetRow = $(this);
                        return false; // Break the loop
                    }
                });

                if (targetRow && targetRow.length) {
                    // Update Total Allowance - use both selectors for compatibility
                    targetRow.find('[data-cell-type="total-allocation"], .total-allocation-cell').text(`£${BudgetUtils.formatNumber(week.total_weekly_allocation)}`);

                    // Update Weekly Balance - use both selectors for compatibility
                    targetRow.find('[data-cell-type="weekly-balance"], .weekly-balance-cell').text(`£${BudgetUtils.formatNumber(week.weekly_unutilised_fund)}`);

                    // Update Cumulative Balance - use both selectors for compatibility
                    targetRow.find('[data-cell-type="cumulative-balance"], .cumulative-balance-cell').text(`£${BudgetUtils.formatNumber(week.consolidated_unutilised_fund)}`);

                    // Update Balance Fund - use both selectors for compatibility
                    targetRow.find('[data-cell-type="balance-fund"], .balance-fund-cell').text(`£${BudgetUtils.formatNumber(week.balance_fund)}`);
                }
            } else {
                console.log('Skipping week', week.week_number, '(before first selected week', firstWeekNumber, ')');
            }
        });
    },

    /**
     * Safely parse a number from text, handling NaN cases
     */
    safeParseNumber: function(text) {
        if (!text || typeof text !== 'string') return 0;

        // Remove currency symbols, commas, and whitespace
        const cleanText = text.replace(/[£,$%\s]/g, '');

        // Parse the number
        const parsed = parseFloat(cleanText);

        // Return 0 if NaN, otherwise return the parsed number
        return isNaN(parsed) ? 0 : parsed;
    },

    /**
     * Perform budget calculations
     */
    performCalculations: function(values) {
        // Ensure all values are numbers, default to 0 if NaN
        const weeklyBudget = isNaN(values.weeklyBudget) ? 0 : values.weeklyBudget;
        const weeklyAllowance = isNaN(values.weeklyAllowance) ? 0 : values.weeklyAllowance;
        const specialAllowance = isNaN(values.specialAllowance) ? 0 : values.specialAllowance;
        const internalTransfers = isNaN(values.internalTransfers) ? 0 : values.internalTransfers;
        const totalUtilisation = isNaN(values.totalUtilisation) ? 0 : values.totalUtilisation;
        const cumulativeBalance = isNaN(values.cumulativeBalance) ? 0 : values.cumulativeBalance;

        const totalAllocation = weeklyBudget + weeklyAllowance + specialAllowance + internalTransfers;
        const percentUtilisation = totalAllocation > 0 ? (totalUtilisation / totalAllocation) * 100 : 0;
        const weeklyBalance = totalAllocation - totalUtilisation;

        // New calculated columns
        const calculatedWeeklyBudgetBalance = weeklyBudget - totalUtilisation;
        const weeklyBudgetUtilisationPercent = weeklyBudget > 0 ? (totalUtilisation / weeklyBudget) * 100 : 0;

        // For individual row calculations, we'll use the current cumulative balance
        // The proper cumulative balance will be calculated later for all rows
        const balanceFund = cumulativeBalance;

        return {
            totalAllocation: isNaN(totalAllocation) ? 0 : totalAllocation,
            percentUtilisation: isNaN(percentUtilisation) ? 0 : percentUtilisation,
            calculatedWeeklyBudgetBalance: isNaN(calculatedWeeklyBudgetBalance) ? 0 : calculatedWeeklyBudgetBalance,
            weeklyBudgetUtilisationPercent: isNaN(weeklyBudgetUtilisationPercent) ? 0 : weeklyBudgetUtilisationPercent,
            weeklyBalance: isNaN(weeklyBalance) ? 0 : weeklyBalance,
            balanceFund: isNaN(balanceFund) ? 0 : balanceFund
        };
    },

    /**
     * Update row cells with calculated values
     */
    updateRowCells: function(row, calculations) {
        // Use both selectors for compatibility
        row.find('[data-cell-type="total-allocation"], .total-allocation-cell').text('£' + BudgetUtils.formatNumber(calculations.totalAllocation));
        row.find('[data-cell-type="calculated-weekly-budget-balance"], .calculated-weekly-budget-balance-cell').text('£' + BudgetUtils.formatNumber(calculations.calculatedWeeklyBudgetBalance));
        row.find('[data-cell-type="weekly-budget-utilisation-percent"], .weekly-budget-utilisation-percent-cell').text(BudgetUtils.formatNumber(calculations.weeklyBudgetUtilisationPercent) + '%');
        row.find('[data-cell-type="percent-utilisation"], .percent-utilisation-cell').text(BudgetUtils.formatNumber(calculations.percentUtilisation) + '%');
        row.find('[data-cell-type="weekly-balance"], .weekly-balance-cell').text('£' + BudgetUtils.formatNumber(calculations.weeklyBalance));

        // Note: Balance Fund will be updated by the cumulative balance recalculation
        // For now, we'll update it with the current calculation, but it will be corrected later
        row.find('[data-cell-type="balance-fund"], .balance-fund-cell').text('£' + BudgetUtils.formatNumber(calculations.balanceFund));
    },

    /**
     * Show save/cancel actions
     */
    showSaveActions: function() {
        $('#recalculateBtn').hide();
        $('#saveActions').show();
    },

    /**
     * Hide save/cancel actions
     */
    hideSaveActions: function() {
        $('#saveActions').hide();
        $('#recalculateBtn').show();
    },

    /**
     * Handle save changes button click
     */
    handleSaveChanges: function() {
        const updatedRows = this.collectUpdatedRows();

        if (updatedRows.length === 0) {
            BudgetUtils.showAlert('No changes to save.', 'info');
            return;
        }

        BudgetUtils.showLoading($('#saveChangesBtn'), 'Saving...');

        $.ajax({
            url: window.routes.updateRecalculated,
            type: 'POST',
            data: {
                _token: window.csrfToken,
                rows: updatedRows
            },
            success: this.handleSaveSuccess.bind(this),
            error: this.handleSaveError.bind(this)
        });
    },

    /**
     * Collect updated rows data
     */
    collectUpdatedRows: function() {
        const updatedRows = [];

        $('.modified-row, [data-row-type="budget-row"].modified-row').each((index, element) => {
            const row = $(element);
            const rowId = row.data('id');

            updatedRows.push({
                id: rowId,
                weekly_budget: this.safeParseNumber(row.find('[data-cell-type="weekly-budget"], .weekly-budget-cell').text()),
                weekly_allowance: this.safeParseNumber(row.find('[data-cell-type="weekly-allowance"], .weekly-allowance-cell').text()),
                special_allowance: this.safeParseNumber(row.find('[data-cell-type="special-allowance"], .special-allowance-cell').text()),
                total_weekly_allocation: this.safeParseNumber(row.find('[data-cell-type="total-allocation"], .total-allocation-cell').text()),
                total_weekly_utilisation: this.safeParseNumber(row.find('[data-cell-type="total-utilisation"], .total-utilisation-cell').text()),
                percent_utilisation: this.safeParseNumber(row.find('[data-cell-type="percent-utilisation"], .percent-utilisation-cell').text()),
                weekly_unutilised_fund: this.safeParseNumber(row.find('[data-cell-type="weekly-balance"], .weekly-balance-cell').text()),
                internal_transfers: this.safeParseNumber(row.find('[data-cell-type="internal-transfers"], .internal-transfers-cell').text()),
                notes: row.find('[data-cell-type="notes"], .notes-cell').text().trim()
            });
        });

        return updatedRows;
    },

    /**
     * Handle save success response
     */
    handleSaveSuccess: function(response) {
        if (response.success) {
            BudgetUtils.showAlert('Changes saved successfully.', 'success');

            // Reset UI - use both selectors for compatibility
            $('.budget-row, [data-row-type="budget-row"]').removeClass('modified-row');
            this.modifiedRows.clear();
            this.hideSaveActions();

            // Reload page after delay
            setTimeout(function() {
                location.reload();
            }, 1500);
        }
    },

    /**
     * Handle save error response
     */
    handleSaveError: function(xhr) {
        BudgetUtils.hideLoading($('#saveChangesBtn'), '<i class="fas fa-save mr-1"></i> Save Changes');
        BudgetUtils.handleAjaxError(xhr, 'An error occurred while saving changes. Please try again.');
    },

    /**
     * Handle cancel changes button click
     */
    handleCancelChanges: function() {
        $('.modified-row').each((index, element) => {
            this.restoreOriginalValues($(element));
        });

        // Reset UI
        this.modifiedRows.clear();
        this.hideSaveActions();
        window.location.reload();
    },

    /**
     * Restore original values for a row
     */
    restoreOriginalValues: function(row) {
        
        const originalValues = row.data('original-values');

        if (!originalValues) return;

        // Restore values from data attribute - use both selectors for compatibility
        row.find('[data-cell-type="weekly-budget"], .weekly-budget-cell').text('£' + BudgetUtils.formatNumber(originalValues.weekly_budget));
        row.find('[data-cell-type="weekly-allowance"], .weekly-allowance-cell').text('£' + BudgetUtils.formatNumber(originalValues.weekly_allowance));
        row.find('[data-cell-type="special-allowance"], .special-allowance-cell').text('£' + BudgetUtils.formatNumber(originalValues.special_allowance));
        row.find('[data-cell-type="total-allocation"], .total-allocation-cell').text('£' + BudgetUtils.formatNumber(originalValues.total_weekly_allocation));
        row.find('[data-cell-type="total-utilisation"], .total-utilisation-cell').text('£' + BudgetUtils.formatNumber(originalValues.total_weekly_utilisation));
        row.find('[data-cell-type="percent-utilisation"], .percent-utilisation-cell').text(BudgetUtils.formatNumber(originalValues.percent_utilisation) + '%');
        row.find('[data-cell-type="weekly-balance"], .weekly-balance-cell').text('£' + BudgetUtils.formatNumber(originalValues.weekly_unutilised_fund));
        row.find('[data-cell-type="cumulative-balance"], .cumulative-balance-cell').text('£' + BudgetUtils.formatNumber(originalValues.consolidated_unutilised_fund));
        
        // Restore internal transfers with button - use both selectors for compatibility
        const weekNumber = row.find('[data-cell-type="week-number"], td:nth-child(2)').text().replace('Week ', '');
        row.find('[data-cell-type="internal-transfers"], .internal-transfers-cell').html(
            '£' + BudgetUtils.formatNumber(originalValues.internal_transfers) +
            `<div class="transfer-buttons mt-1">
                <button type="button" class="btn btn-sm btn-primary"
                        data-action="transfer" data-button-type="transfer"
                        data-week="${weekNumber}" data-unit="${window.unitId}"
                        data-balance="${originalValues.balance_fund}" title="Make Transfer">
                    <i class="fa fa-exchange"></i>
                </button>
                <button type="button" class="btn btn-sm btn-info ml-1"
                        data-action="transfer-history" data-button-type="transfer-history"
                        data-week="${weekNumber}" data-unit="${window.unitId}"
                        data-unit-name="${window.unitName || ''}" title="View Transfer History">
                    <i class="fa fa-info"></i>
                </button>
            </div>`
        );

        row.find('[data-cell-type="balance-fund"], .balance-fund-cell').text('£' + BudgetUtils.formatNumber(originalValues.balance_fund));
        row.find('[data-cell-type="notes"], .notes-cell').text(originalValues.notes);

        // Remove modified class
        row.removeClass('modified-row');
    },

    /**
     * Add CSS styles for modified rows
     */
    addStyles: function() {
        if (!$('#budget-recalculate-styles').length) {
            $('<style id="budget-recalculate-styles">')
                .text('.modified-row { background-color: #fffde7 !important; } .modified-row td { font-weight: bold; }')
                .appendTo('head');
        }
    }
};
