/**
 * Budget Summary Card Enhancement Module
 * Adds interactive features and animations to the budget summary card
 */

window.BudgetSummaryCard = {
    /**
     * Initialize the budget summary card enhancements
     */
    init: function() {
        this.setupAnimations();
        this.setupTooltips();
        this.setupRefreshButton();
        this.addCountUpAnimations();
    },

    /**
     * Setup entrance animations for the summary card
     */
    setupAnimations: function() {
        const summaryCard = $('#budgetSummaryCard');
        if (summaryCard.length === 0) return;

        // Add entrance animation
        summaryCard.css({
            'opacity': '0',
            'transform': 'translateY(30px)'
        });

        // Animate in after a short delay
        setTimeout(() => {
            summaryCard.animate({
                'opacity': '1'
            }, 600);
            
            summaryCard.css({
                'transform': 'translateY(0)',
                'transition': 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            });
        }, 200);

        // Stagger animation for sections
        $('.summary-section').each(function(index) {
            const section = $(this);
            section.css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            });

            setTimeout(() => {
                section.animate({
                    'opacity': '1'
                }, 400);
                
                section.css({
                    'transform': 'translateY(0)',
                    'transition': 'all 0.4s ease'
                });
            }, 400 + (index * 150));
        });
    },

    /**
     * Setup enhanced tooltips for summary elements
     */
    setupTooltips: function() {
        // Add tooltips for configuration items
        $('.config-item').each(function() {
            const item = $(this);
            const label = item.find('.config-label').text().trim();
            const value = item.find('.config-value').text().trim();
            
            let tooltipText = '';
            switch(true) {
                case label.includes('Total Weeks'):
                    tooltipText = 'Total number of weeks generated for this financial year';
                    break;
                case label.includes('Week Interval'):
                    tooltipText = 'Number of days between each week period';
                    break;
                case label.includes('Period End'):
                    tooltipText = 'Last week ending date in the budget allocation';
                    break;
                case label.includes('Avg. Allowance'):
                    tooltipText = 'Average percentage of weekly budget allocated as allowance';
                    break;
            }
            
            if (tooltipText) {
                item.attr('data-toggle', 'tooltip')
                    .attr('data-placement', 'top')
                    .attr('title', tooltipText);
            }
        });

        // Add tooltip for utilization status
        $('.utilization-indicator').attr('data-toggle', 'tooltip')
            .attr('data-placement', 'top')
            .attr('title', 'Overall budget utilization percentage across all weeks');

        // Initialize Bootstrap tooltips
        $('[data-toggle="tooltip"]').tooltip();
    },

    /**
     * Add refresh functionality
     */
    setupRefreshButton: function() {
        // Add refresh button to summary header if it doesn't exist
        const header = $('.budget-summary-header');
        if (header.length && !header.find('.refresh-summary-btn').length) {
            const refreshBtn = $(`
                <button type="button" class="refresh-summary-btn" data-toggle="tooltip"
                        data-placement="top" title="Refresh summary data">
                    <i class="fa fa-refresh"></i>
                </button>
            `);

            header.append(refreshBtn);

            refreshBtn.on('click', this.refreshSummaryData.bind(this));
        }
    },

    /**
     * Add count-up animations for numerical values
     */
    addCountUpAnimations: function() {
        // Animate the main annual budget value
        this.animateValue('.summary-main-value', 0, null, 1500, '£');
        
        // Animate breakdown values
        $('.breakdown-value').each(function() {
            const element = $(this);
            if (element.text().includes('£')) {
                BudgetSummaryCard.animateValue(element, 0, null, 1000, '£');
            }
        });

        // Animate config values
        $('.config-value').each(function() {
            const element = $(this);
            const text = element.text().trim();
            
            if (text.match(/^\d+/)) {
                const value = parseFloat(text.replace(/[^\d.]/g, ''));
                const suffix = text.replace(/[\d.,]/g, '').trim();
                BudgetSummaryCard.animateValue(element, 0, value, 800, '', suffix);
            }
        });
    },

    /**
     * Animate numerical values with count-up effect
     */
    animateValue: function(selector, startValue, endValue, duration, prefix = '', suffix = '') {
        const element = $(selector);
        if (element.length === 0) return;

        if (endValue === null) {
            // Extract value from existing text
            const text = element.text().trim();
            endValue = parseFloat(text.replace(/[^\d.,]/g, '').replace(/,/g, ''));
            if (isNaN(endValue)) return;
        }

        const startTime = Date.now();
        const startVal = startValue;
        const endVal = endValue;

        const updateValue = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Use easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = startVal + (endVal - startVal) * easeOutQuart;
            
            // Format the value
            let formattedValue = this.formatNumber(currentValue);
            if (prefix) formattedValue = prefix + formattedValue;
            if (suffix) formattedValue = formattedValue + suffix;
            
            element.text(formattedValue);

            if (progress < 1) {
                requestAnimationFrame(updateValue);
            }
        };

        // Start animation after summary card is visible
        setTimeout(() => {
            requestAnimationFrame(updateValue);
        }, 800);
    },

    /**
     * Format numbers with proper thousand separators
     */
    formatNumber: function(value) {
        if (value >= 1) {
            return Math.round(value).toLocaleString();
        } else {
            return value.toFixed(2);
        }
    },

    /**
     * Refresh summary data
     */
    refreshSummaryData: function() {
        const refreshBtn = $('.refresh-summary-btn');
        const icon = refreshBtn.find('i');
        
        // Add spinning animation
        icon.addClass('fa-spin');
        refreshBtn.prop('disabled', true);
        
        // Simulate refresh (in real implementation, this would make an AJAX call)
        setTimeout(() => {
            icon.removeClass('fa-spin');
            refreshBtn.prop('disabled', false);
            
            // Show success notification
            this.showRefreshNotification();
            
            // Re-animate values
            this.addCountUpAnimations();
        }, 1500);
    },

    /**
     * Show refresh notification
     */
    showRefreshNotification: function() {
        // Create a temporary notification
        const notification = $(`
            <div class="summary-refresh-notification">
                <i class="fa fa-check-circle"></i>
                Summary data refreshed
            </div>
        `);
        
        $('#budgetSummaryCard').append(notification);
        
        // Animate in
        notification.fadeIn(300);
        
        // Remove after delay
        setTimeout(() => {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    },

    /**
     * Add hover effects for interactive elements
     */
    setupHoverEffects: function() {
        // Enhanced hover for summary sections
        $('.summary-section').on('mouseenter', function() {
            $(this).css({
                'transform': 'translateY(-3px) scale(1.01)',
                'box-shadow': '0 15px 45px rgba(0, 0, 0, 0.2)'
            });
        }).on('mouseleave', function() {
            $(this).css({
                'transform': 'translateY(0) scale(1)',
                'box-shadow': '0 8px 32px rgba(0, 0, 0, 0.1)'
            });
        });

        // Pulse effect for important values
        $('.breakdown-value.highlighted').on('mouseenter', function() {
            $(this).css({
                'animation': 'pulse 0.5s ease-in-out'
            });
        });
    }
};

// Additional CSS for refresh button and notification
const additionalCSS = `
<style>
.refresh-summary-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 10;
}

.refresh-summary-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.refresh-summary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.summary-refresh-notification {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(39, 174, 96, 0.95);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 500;
    font-size: 14px;
    display: none;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(39, 174, 96, 0.4);
}

.summary-refresh-notification i {
    margin-right: 8px;
    font-size: 16px;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}
</style>
`;

// Add the additional CSS to the page
$('head').append(additionalCSS);

// Initialize when document is ready
$(document).ready(function() {
    // Initialize summary card enhancements if the card exists
    if ($('#budgetSummaryCard').length > 0) {
        BudgetSummaryCard.init();
        BudgetSummaryCard.setupHoverEffects();
    }
});
