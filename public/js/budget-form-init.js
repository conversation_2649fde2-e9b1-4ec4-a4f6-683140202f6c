/**
 * Budget Form Initialization Script
 * Handles global initialization for budget forms
 */

window.BudgetFormInit = {
    /**
     * Initialize budget form functionality
     */
    init: function() {
        this.setupGlobalAlertTimeout();
        this.preventNumberFormatting();
    },

    /**
     * Setup global alert auto-hide timeout
     * Excludes alerts with 'custom-alert-no-auto-hide' class
     */
    setupGlobalAlertTimeout: function() {
        window.setTimeout(function() {
            $(".alert:not(.custom-alert-no-auto-hide)").fadeTo(500, 0).slideUp(500, function() {
                $(this).remove();
            });
        }, 4000);
    },

    /**
     * Prevent automatic number formatting for date fields
     */
    preventNumberFormatting: function() {
        $(document).ready(function() {
            // Override any automatic number formatting for elements with data-no-format="true"
            $('[data-no-format="true"]').each(function() {
                const originalText = $(this).text();
                // Store original text and prevent any number formatting
                $(this).data('original-text', originalText);

                // Create a mutation observer to prevent changes
                if (window.MutationObserver) {
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList' || mutation.type === 'characterData') {
                                const currentText = $(mutation.target).text();
                                const originalText = $(mutation.target).data('original-text');
                                if (originalText && currentText !== originalText && currentText.includes(',')) {
                                    $(mutation.target).text(originalText);
                                }
                            }
                        });
                    });
                    observer.observe(this, { childList: true, characterData: true, subtree: true });
                }
            });
        });
    }
};

// Auto-initialize when DOM is ready
$(document).ready(function() {
    if (typeof window.routes !== 'undefined') {
        // Only initialize if we have the routes object (budget form context)
        BudgetFormInit.init();
    }
});
