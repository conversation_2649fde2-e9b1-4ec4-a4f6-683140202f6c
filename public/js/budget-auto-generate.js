/**
 * Budget Auto Generation Module
 * Handles automatic generation of weekly budgets
 */

window.BudgetAutoGenerate = {
    /**
     * Initialize auto-generation functionality
     */
    init: function() {
        this.bindEvents();
        this.checkExistingWeeks();
        this.checkUrlParams();
    },

    /**
     * Bind event handlers
     */
    bindEvents: function() {
        // Use data attributes for button actions
        $('[data-action="preview-generate"]').on('click', this.previewGeneratedWeeks.bind(this));
        $(document).on('click', '[data-action="save-generated"]', this.saveGeneratedWeeks.bind(this));
        $(document).on('click', '[data-action="cancel-generated"]', this.cancelGeneration.bind(this));

        // Auto-generate input change handlers
        $('[data-auto-generate-input]').on('input change', this.handleInputChange.bind(this));
    },

    /**
     * Handle input changes for auto-generate form
     */
    handleInputChange: function(event) {
        const input = $(event.target);
        const inputType = input.data('input-type');

        // Validate input based on type
        switch(inputType) {
            case 'number-of-weeks':
                this.validateNumberOfWeeks(input);
                break;
            case 'week-interval':
                this.validateWeekInterval(input);
                break;
            case 'weekly-allowance-percentage':
                this.validatePercentage(input);
                break;
        }
    },

    /**
     * Validate number of weeks input
     */
    validateNumberOfWeeks: function(input) {
        const value = parseInt(input.val());
        if (value < 1 || value > 53) {
            input.addClass('is-invalid');
            BudgetUtils.showAlert('Number of weeks must be between 1 and 53.', 'warning');
        } else {
            input.removeClass('is-invalid');
        }
    },

    /**
     * Validate week interval input
     */
    validateWeekInterval: function(input) {
        const value = parseInt(input.val());
        if (value < 1 || value > 14) {
            input.addClass('is-invalid');
            BudgetUtils.showAlert('Week interval must be between 1 and 14 days.', 'warning');
        } else {
            input.removeClass('is-invalid');
        }
    },

    /**
     * Validate percentage input
     */
    validatePercentage: function(input) {
        const value = parseFloat(input.val());
        if (value < 0 || value > 100) {
            input.addClass('is-invalid');
            BudgetUtils.showAlert('Percentage must be between 0 and 100.', 'warning');
        } else {
            input.removeClass('is-invalid');
        }
    },

    /**
     * Preview generated weeks
     */
    previewGeneratedWeeks: function() {
        const annualBudget = parseFloat($('#annual_budget').val());
        if (!annualBudget || annualBudget <= 0) {
            BudgetUtils.showAutoGenerateAlert('Please enter a valid Annual Budget amount first.');
            return;
        }

        const weeklyAllowancePercentage = parseFloat($('#weekly_allowance_percentage').val());
        if (isNaN(weeklyAllowancePercentage) || weeklyAllowancePercentage < 0 || weeklyAllowancePercentage > 100) {
            BudgetUtils.showAutoGenerateAlert('Weekly Allowance % must be between 0 and 100.');
            return;
        }

        $('#auto_annual_budget').val(annualBudget);

        const formData = this.collectFormData();
        const validation = BudgetUtils.validateFormData(formData);

        if (!validation.isValid) {
            BudgetUtils.showAutoGenerateAlert(validation.errors.join('<br>'));
            return;
        }

        BudgetUtils.showLoading($('#previewGenerateBtn'), 'Generating...');

        $.ajax({
            url: window.routes.generateWeeks,
            type: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': window.csrfToken
            },
            success: this.handlePreviewSuccess.bind(this),
            error: this.handlePreviewError.bind(this),
            complete: function() {
                BudgetUtils.hideLoading($('#previewGenerateBtn'), 'Preview Generated Weeks');
            }
        });
    },

    /**
     * Collect form data for generation
     */
    collectFormData: function() {
        return {
            unit_id: $('input[name="unit_id"]').val(),
            financial_year: $('input[name="financial_year"]').val(),
            annual_budget: $('#auto_annual_budget').val(),
            first_end_date: $('#first_end_date').val(),
            week_interval: $('#week_interval').val(),
            number_of_weeks: $('#number_of_weeks').val(),
            weekly_allowance_percentage: parseFloat($('#weekly_allowance_percentage').val()),
            special_allowance: 0
        };
    },

    /**
     * Handle preview success response
     */
    handlePreviewSuccess: function(response) {
        if (response.success) {
            this.populatePreviewTable(response.weeks);
            $('#previewSection').show();
            $('html, body').animate({
                scrollTop: $('#previewSection').offset().top - 100
            }, 500);
        }
    },

    /**
     * Handle preview error response
     */
    handlePreviewError: function(xhr) {
        BudgetUtils.handleAjaxError(xhr, 'An error occurred while generating weeks. Please try again.');
    },

    /**
     * Populate preview table with generated weeks
     */
    populatePreviewTable: function(weeks) {
        const tableBody = $('#previewTableBody');
        tableBody.empty();

        const sortedWeeks = Object.values(weeks).sort((a, b) => a.week_number - b.week_number);

        sortedWeeks.forEach(week => {
            const row = this.createPreviewRow(week);
            tableBody.append(row);
        });

        // Force table layout recalculation
        this.fixTableAlignment();
    },

    /**
     * Fix table alignment after data population
     */
    fixTableAlignment: function() {
        // Force the browser to recalculate table layout
        const previewTable = $('#previewTable');
        
        // Temporarily hide and show to trigger layout recalculation
        previewTable.hide().show();
        
        // Ensure consistent column widths
        previewTable.find('th, td').each(function(index) {
            const columnIndex = (index % 12) + 1;
            let width;
            switch(columnIndex) {
                case 1: width = '8%'; break;     // Week
                case 2: width = '9%'; break;     // Week Ending
                case 3: width = '8.5%'; break;   // Weekly Budget
                case 4: width = '8.5%'; break;   // Weekly Allowance
                case 5: width = '8.5%'; break;   // Special Allowance
                case 6: width = '9%'; break;     // Total Allowance
                case 7: width = '8.5%'; break;   // Total Utilisation
                case 8: width = '8.5%'; break;   // Budget Balance
                case 9: width = '8.5%'; break;   // Budget Utilisation %
                case 10: width = '8.5%'; break;  // Weekly Balance
                case 11: width = '8%'; break;    // % Utilisation
                case 12: width = '9.5%'; break;  // Consolidated Fund
            }
            $(this).css('width', width);
        });
    },

    /**
     * Create preview table row
     */
    createPreviewRow: function(week) {
        return `
            <tr>
                <td style="text-align: center; width: 8%;">Week ${week.week_number}</td>
                <td style="text-align: center; width: 9%;">${BudgetUtils.formatDate(week.end_date)}</td>
                <td style="text-align: right; width: 8.5%;">£${BudgetUtils.formatNumber(week.weekly_budget)}</td>
                <td style="text-align: right; width: 8.5%;">£${BudgetUtils.formatNumber(week.weekly_allowance)}</td>
                <td style="text-align: right; width: 8.5%;">£${BudgetUtils.formatNumber(week.special_allowance)}</td>
                <td style="text-align: right; width: 9%;">£${BudgetUtils.formatNumber(week.total_weekly_allocation)}</td>
                <td style="text-align: right; width: 8.5%;">£${BudgetUtils.formatNumber(week.total_weekly_utilisation)}</td>
                <td style="text-align: right; width: 8.5%;">£${BudgetUtils.formatNumber(week.weekly_budget - week.total_weekly_utilisation)}</td>
                <td style="text-align: right; width: 8.5%;">${BudgetUtils.formatNumber(week.weekly_budget > 0 ? (week.total_weekly_utilisation / week.weekly_budget) * 100 : 0)}%</td>
                <td style="text-align: right; width: 8.5%;">£${BudgetUtils.formatNumber(week.weekly_unutilised_fund)}</td>
                <td style="text-align: right; width: 8%;">${BudgetUtils.formatNumber(week.percent_utilisation)}%</td>
                <td style="text-align: right; width: 9.5%;">£${BudgetUtils.formatNumber(week.consolidated_unutilised_fund)}</td>
            </tr>
        `;
    },

    /**
     * Save generated weeks
     */
    saveGeneratedWeeks: function() {
        if (!confirm("Once the generation is saved, you can't re-generate weekly budgets. Are you sure you want to proceed?")) {
            return;
        }

        const annualBudget = parseFloat($('#annual_budget').val());
        if (!annualBudget || annualBudget <= 0) {
            BudgetUtils.showAlert('Please enter a valid Annual Budget amount first.', 'error');
            return;
        }

        const weeks = this.extractWeeksFromPreview();
        if (weeks.length === 0) {
            BudgetUtils.showAlert('No valid weeks found to save. Please check the data and try again.', 'error');
            return;
        }

        BudgetUtils.showLoading($('#saveGeneratedBtn'), 'Saving...');

        $.ajax({
            url: window.routes.saveGeneratedWeeks,
            type: 'POST',
            data: {
                _token: window.csrfToken,
                unit_id: window.unitId,
                financial_year: parseInt(window.financialYear),
                annual_budget: annualBudget,
                weeks: weeks
            },
            success: this.handleSaveSuccess.bind(this),
            error: this.handleSaveError.bind(this)
        });
    },

    /**
     * Extract weeks data from preview table
     */
    extractWeeksFromPreview: function() {
        const weeks = [];
        const weekInterval = parseInt($('#week_interval').val()) || 7;

        $('#previewTableBody tr').each(function() {
            const cells = $(this).find('td');
            const weekNumber = parseInt(cells.eq(0).text().replace('Week ', ''));
            const endDateDisplay = cells.eq(1).text().trim();
            const endDate = BudgetUtils.formatDateForInput(endDateDisplay);

            if (!endDate) {
                console.error('Invalid date format:', endDateDisplay);
                return;
            }

            const endDateObj = new Date(endDate);
            if (isNaN(endDateObj.getTime())) {
                console.error('Invalid end date:', endDate);
                return;
            }

            const startDateObj = new Date(endDateObj);
            startDateObj.setDate(endDateObj.getDate() - (weekInterval - 1));
            const startDate = startDateObj.toISOString().split('T')[0];

            weeks.push({
                week_number: weekNumber,
                start_date: startDate,
                end_date: endDate,
                weekly_budget: BudgetUtils.parseCurrency(cells.eq(2).text()),
                weekly_allowance: BudgetUtils.parseCurrency(cells.eq(3).text()),
                special_allowance: BudgetUtils.parseCurrency(cells.eq(4).text()),
                total_weekly_allocation: BudgetUtils.parseCurrency(cells.eq(5).text()),
                total_weekly_utilisation: BudgetUtils.parseCurrency(cells.eq(6).text()),
                calculated_weekly_budget_balance: BudgetUtils.parseCurrency(cells.eq(7).text()),
                weekly_budget_utilisation_percent: parseFloat(cells.eq(8).text().replace('%', '').replace(/,/g, '')) || 0,
                weekly_unutilised_fund: BudgetUtils.parseCurrency(cells.eq(9).text()),
                percent_utilisation: parseFloat(cells.eq(10).text().replace('%', '').replace(/,/g, '')) || 0,
                consolidated_unutilised_fund: BudgetUtils.parseCurrency(cells.eq(11).text()),
                internal_transfers: 0, // Default value since not displayed in preview
                balance_fund: 0, // Will be calculated by backend
                notes: '' // Notes not included in preview
            });
        });

        return weeks;
    },

    /**
     * Handle save success response
     */
    handleSaveSuccess: function(response) {
        const successUrl = `${window.routes.budgetEdit}?success=Weeks auto-generation saved. Further auto-generation is disabled.`;
        window.location.href = successUrl;
    },

    /**
     * Handle save error response
     */
    handleSaveError: function(xhr) {
        BudgetUtils.hideLoading($('#saveGeneratedBtn'), 'Save and Proceed');
        BudgetUtils.handleAjaxError(xhr, 'An error occurred while saving generated weeks.');
    },

    /**
     * Cancel generation
     */
    cancelGeneration: function() {
        $('#previewSection').hide();
    },

    /**
     * Check if weeks already exist and disable auto-generate if needed
     */
    checkExistingWeeks: function() {
        const hasExistingWeeks = $('#weeklyBudgetsTable tbody tr').length > 0 &&
                                !$('#weeklyBudgetsTable tbody tr td').hasClass('text-center');

        if (hasExistingWeeks) {
            $('.message-container').append('<div class="alert alert-info mt-3">Auto-generation is disabled because weekly budgets already exist.</div>');
            $('#previewGenerateBtn').prop('disabled', true);
        } else {
            $('#weekly_allowance_percentage').prop('disabled', false);
        }
    },

    /**
     * Check URL parameters for success messages
     */
    checkUrlParams: function() {
        const urlParams = new URLSearchParams(window.location.search);
        const successMessage = urlParams.get('success');
        if (successMessage) {
            // Show success message in the auto-generate section
            BudgetUtils.showAlert(successMessage, 'success', $('#autoGenerateSection'));
            const newUrl = window.location.pathname + window.location.search.replace(/[?&]success=[^&]+/, '');
            window.history.replaceState({}, document.title, newUrl);
        }
    }
};
