/**
 * Budget Form Utilities
 * Common utility functions for budget form operations
 */

// Global utility functions
window.BudgetUtils = {
    /**
     * Format number with commas and decimal places
     * @param {number} num - Number to format
     * @returns {string} Formatted number
     */
    formatNumber: function(num) {
        return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    /**
     * Parse currency string to number
     * @param {string} currencyStr - Currency string (e.g., "£1,234.56")
     * @returns {number} Parsed number
     */
    parseCurrency: function(currencyStr) {
        return parseFloat(currencyStr.replace(/[£,]/g, '')) || 0;
    },

    /**
     * Format date for display (DD MMM YYYY)
     * @param {string} dateString - Date string
     * @returns {string} Formatted date
     */
    formatDate: function(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';

        const day = date.getDate().toString().padStart(2, '0');
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const month = monthNames[date.getMonth()];
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
    },

    /**
     * Format date for input field (YYYY-MM-DD)
     * @param {string} displayDate - Display date (DD MMM YYYY)
     * @returns {string} Input date format
     */
    formatDateForInput: function(displayDate) {
        if (!displayDate || typeof displayDate !== 'string') return '';

        const parts = displayDate.trim().split(' ');
        if (parts.length !== 3) return '';

        const day = parts[0];
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const month = monthNames.indexOf(parts[1]) + 1;
        const year = parts[2];

        if (month === 0 || isNaN(parseInt(day)) || isNaN(parseInt(year))) return '';

        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    },

    /**
     * Show loading state on button
     * @param {jQuery} button - Button element
     * @param {string} text - Loading text
     */
    showLoading: function(button, text = 'Loading...') {
        button.prop('disabled', true).html(`<i class="fas fa-spinner fa-spin"></i> ${text}`);
    },

    /**
     * Hide loading state on button
     * @param {jQuery} button - Button element
     * @param {string} originalText - Original button text
     */
    hideLoading: function(button, originalText) {
        button.prop('disabled', false).html(originalText);
    },

    /**
     * Clear existing alerts from recalculate container
     */
    clearRecalculateAlerts: function() {
        const container = $('#budget-recalculate-alerts-container');
        if (container.length) {
            container.empty().hide();
        }
    },

    /**
     * Show simple recalculate alert in red color
     */
    showRecalculateAlert: function(message) {
        const container = $('#budget-recalculate-alerts-container');
        if (container.length) {
            container.empty().show();
            container.append('<div class="alert alert-danger mt-3">' + message + '</div>');

            // Auto-hide after 5 seconds
            setTimeout(function() {
                container.fadeOut(300, function() {
                    container.empty().hide();
                });
            }, 5000);
        }
    },

    /**
     * Show simple auto-generate alert in red color
     */
    showAutoGenerateAlert: function(message) {
        const container = $('#auto-generate-alerts-container');
        if (container.length) {
            container.empty().show();
            container.append('<div class="alert alert-danger mt-3">' + message + '</div>');

            // Auto-hide after 5 seconds
            setTimeout(function() {
                container.fadeOut(300, function() {
                    container.empty().hide();
                });
            }, 5000);
        }
    },

    /**
     * Show alert message
     * @param {string} message - Alert message
     * @param {string} type - Alert type (success, error, info, warning)
     * @param {jQuery} container - Container to insert alert
     */
    showAlert: function(message, type = 'info', container = null) {
        const alertId = 'alert-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);

        // Use the same color scheme as showSimpleAlert for consistency
        const colors = {
            'success': { bg: '#d4edda', text: '#155724', border: '#c3e6cb' },
            'error': { bg: '#f8d7da', text: '#721c24', border: '#f5c6cb' },
            'danger': { bg: '#f8d7da', text: '#721c24', border: '#f5c6cb' },
            'warning': { bg: '#fff3cd', text: '#856404', border: '#ffeaa7' },
            'info': { bg: '#d1ecf1', text: '#0c5460', border: '#bee5eb' },
            'primary': { bg: '#cce7ff', text: '#004085', border: '#b3d7ff' },
            'secondary': { bg: '#e2e3e5', text: '#383d41', border: '#d6d8db' }
        };

        const alertType = type === 'error' ? 'danger' : type;
        const color = colors[alertType] || colors['info'];
        const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;

        // Create alert HTML using the same inline styling approach as showSimpleAlert
        const alertHtml = `
            <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert" style="
                background-color: ${color.bg};
                color: ${color.text};
                border: 1px solid ${color.border};
                padding: 12px 16px;
                margin-bottom: 1rem;
                border-radius: 4px;
               font-family: "avenir-lt-pro", sans-serif;
                font-size: 14px;
                font-weight: 500;
                position: relative;
                text-shadow: none;
            ">
                <span style="color: ${color.text};">${message}</span>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close" style="
                    position: absolute;
                    right: 12px;
                    top: 8px;
                    background: none;
                    border: none;
                    font-size: 20px;
                    color: ${color.text};
                    cursor: pointer;
                    opacity: 0.7;
                    font-weight: bold;
                    line-height: 1;
                    text-shadow: none;
                    padding: 0;
                    margin: 0;
                ">
                    <span aria-hidden="true" style="color: ${color.text};">&times;</span>
                </button>
            </div>
        `;

        let targetContainer;
        if (container && container.length) {
            targetContainer = container;
        } else {
            // Check if we have the recalculate alerts container (positioned after budget summary)
            if ($('#budget-recalculate-alerts-container').length) {
                targetContainer = $('#budget-recalculate-alerts-container');
                // Show the container when we add an alert
                targetContainer.show();
            } else {
                // Fallback to the general alerts container at the top
                if (!$('#budget-alerts-container').length) {
                    $('.content').prepend('<div id="budget-alerts-container"></div>');
                }
                targetContainer = $('#budget-alerts-container');
            }
        }

        targetContainer.prepend(alertHtml);

        // Additional color forcing as backup (same as before)
        setTimeout(function() {
            const alertElement = $('#' + alertId);
            if (alertElement.length) {
                alertElement.css('color', color.text);
                alertElement.find('*').css('color', color.text);
                alertElement.find('span').css('color', color.text);
                alertElement.find('.close').css('color', color.text);

                console.log('Alert created with ID:', alertId, 'Type:', alertType, 'Colors:', color);
            }
        }, 100);

        // Auto-hide after 5 seconds with proper cleanup (preserved functionality)
        setTimeout(function() {
            const alertElement = $('#' + alertId);
            if (alertElement.length) {
                alertElement.fadeOut(300, function() {
                    $(this).remove();
                    // Clean up empty containers if no more alerts
                    if ($('#budget-alerts-container').children().length === 0) {
                        $('#budget-alerts-container').remove();
                    }
                    if ($('#budget-recalculate-alerts-container').children().length === 0) {
                        $('#budget-recalculate-alerts-container').hide();
                    }
                });
            }
        }, 5000);
    },

    /**
     * Simple fallback alert with basic styling (for testing)
     */
    showSimpleAlert: function(message, type = 'info') {
        const colors = {
            'success': { bg: '#d4edda', text: '#155724', border: '#c3e6cb' },
            'error': { bg: '#f8d7da', text: '#721c24', border: '#f5c6cb' },
            'danger': { bg: '#f8d7da', text: '#721c24', border: '#f5c6cb' },
            'warning': { bg: '#fff3cd', text: '#856404', border: '#ffeaa7' },
            'info': { bg: '#d1ecf1', text: '#0c5460', border: '#bee5eb' }
        };

        const alertType = type === 'error' ? 'danger' : type;
        const color = colors[alertType] || colors['info'];

        const alertHtml = `
            <div style="
                background-color: ${color.bg};
                color: ${color.text};
                border: 1px solid ${color.border};
                padding: 12px 16px;
                margin: 10px 0;
                border-radius: 4px;
                font-family: "avenir-lt-pro", sans-serif;
                font-size: 14px;
                position: relative;
            ">
                ${message}
                <button onclick="this.parentElement.remove()" style="
                    position: absolute;
                    right: 10px;
                    top: 8px;
                    background: none;
                    border: none;
                    font-size: 18px;
                    color: ${color.text};
                    cursor: pointer;
                ">&times;</button>
            </div>
        `;

        // Use the same container logic as showAlert
        let targetContainer;
        if ($('#budget-recalculate-alerts-container').length) {
            targetContainer = $('#budget-recalculate-alerts-container');
            targetContainer.show();
        } else {
            targetContainer = $('.content');
        }

        if (targetContainer.is('.content')) {
            targetContainer.prepend(alertHtml);
        } else {
            targetContainer.append(alertHtml);
        }
    },

    /**
     * Validate form data
     * @param {Object} data - Form data to validate
     * @returns {Object} Validation result
     */
    validateFormData: function(data) {
        const errors = [];

        if (!data.annual_budget || data.annual_budget <= 0) {
            errors.push('Annual budget must be greater than 0');
        }

        if (data.weekly_allowance_percentage && (data.weekly_allowance_percentage < 0 || data.weekly_allowance_percentage > 100)) {
            errors.push('Weekly allowance percentage must be between 0 and 100');
        }

        if (!data.first_end_date) {
            errors.push('First week end date is required');
        }

        if (!data.number_of_weeks || data.number_of_weeks <= 0) {
            errors.push('Number of weeks must be greater than 0');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    /**
     * Handle AJAX errors
     * @param {Object} xhr - XMLHttpRequest object
     * @param {string} defaultMessage - Default error message
     */
    handleAjaxError: function(xhr, defaultMessage = 'An error occurred. Please try again.') {
        let errorMessage = defaultMessage;

        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
            const errors = xhr.responseJSON.errors;
            errorMessage = '';
            for (const key in errors) {
                errorMessage += errors[key][0] + '<br>';
            }
        } else if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        }

        // Use styled alert instead of browser alert
        this.showAlert(errorMessage, 'error');
        console.error('AJAX Error:', xhr.responseText);
    },

    /**
     * Debounce function to limit function calls
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Calculate cumulative balance for all weeks (mirrors backend logic)
     * @param {Array} weeks - Array of week data objects
     * @param {number} startWeekNumber - Week number to start recalculation from
     * @returns {Array} Updated weeks with recalculated cumulative balances
     */
    recalculateCumulativeBalances: function(weeks, startWeekNumber = 1) {
        let consolidatedFund = 0;

        // Sort weeks by week number
        weeks.sort((a, b) => a.week_number - b.week_number);

        weeks.forEach(week => {
            // For weeks before the start week, preserve their consolidated fund
            if (week.week_number < startWeekNumber) {
                consolidatedFund = week.consolidated_unutilised_fund || 0;
                return;
            }

            // Recalculate total weekly allocation
            week.total_weekly_allocation = (week.weekly_budget || 0) +
                                         (week.weekly_allowance || 0) +
                                         (week.special_allowance || 0) +
                                         (week.internal_transfers || 0);

            // Calculate weekly unutilised fund (Weekly Balance)
            week.weekly_unutilised_fund = week.total_weekly_allocation - (week.total_weekly_utilisation || 0);

            // Calculate Cumulative Balance:
            // Week 1: Cumulative Balance = Week 1's Weekly Balance
            // Week n: Cumulative Balance = Week n's Weekly Balance + Week (n-1)'s Cumulative Balance
            if (week.week_number === 1) {
                consolidatedFund = week.weekly_unutilised_fund;
            } else {
                consolidatedFund += week.weekly_unutilised_fund;
            }
            week.consolidated_unutilised_fund = consolidatedFund;

            // Update balance fund - Balance Fund = Cumulative Balance (internal transfers already included)
            week.balance_fund = consolidatedFund;
        });

        return weeks;
    },

    /**
     * Update cumulative balances in the table after a change
     * @param {number} changedWeekNumber - Week number that was changed
     */
    updateTableCumulativeBalances: function(changedWeekNumber) {
        const tableRows = $('#weeklyBudgetsTable tbody tr[data-original-values]');
        const weeks = [];

        // Extract current data from table rows
        tableRows.each(function() {
            const row = $(this);
            const originalData = JSON.parse(row.attr('data-original-values') || '{}');

            if (originalData.week_number) {
                const weekData = {
                    week_number: originalData.week_number,
                    weekly_budget: BudgetUtils.parseCurrency(row.find('.weekly-budget-cell').text()),
                    weekly_allowance: BudgetUtils.parseCurrency(row.find('.weekly-allowance-cell').text()),
                    special_allowance: BudgetUtils.parseCurrency(row.find('.special-allowance-cell').text()),
                    internal_transfers: BudgetUtils.parseCurrency(row.find('.internal-transfers-cell').text()),
                    total_weekly_utilisation: BudgetUtils.parseCurrency(row.find('.total-utilisation-cell').text()),
                    consolidated_unutilised_fund: BudgetUtils.parseCurrency(row.find('.cumulative-balance-cell').text())
                };
                weeks.push(weekData);
            }
        });

        // Recalculate cumulative balances
        const updatedWeeks = this.recalculateCumulativeBalances(weeks, changedWeekNumber);

        // Update the table display
        updatedWeeks.forEach(week => {
            // Find the row using a more reliable method
            let targetRow = null;
            tableRows.each(function() {
                const originalData = JSON.parse($(this).attr('data-original-values') || '{}');
                if (originalData.week_number == week.week_number) {
                    targetRow = $(this);
                    return false; // Break the loop
                }
            });

            if (targetRow && targetRow.length) {
                // Update Total Allowance - use both selectors for compatibility
                targetRow.find('[data-cell-type="total-allocation"], .total-allocation-cell').text(`£${this.formatNumber(week.total_weekly_allocation)}`);

                // Update Weekly Balance - use both selectors for compatibility
                targetRow.find('[data-cell-type="weekly-balance"], .weekly-balance-cell').text(`£${this.formatNumber(week.weekly_unutilised_fund)}`);

                // Update Cumulative Balance - use both selectors for compatibility
                targetRow.find('[data-cell-type="cumulative-balance"], .cumulative-balance-cell').text(`£${this.formatNumber(week.consolidated_unutilised_fund)}`);

                // Update Balance Fund - use both selectors for compatibility
                targetRow.find('[data-cell-type="balance-fund"], .balance-fund-cell').text(`£${this.formatNumber(week.balance_fund)}`);
            }
        });
    }
};

// Make formatNumber available globally for backward compatibility
window.formatNumber = BudgetUtils.formatNumber;
