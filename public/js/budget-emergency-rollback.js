/**
 * Budget Form Emergency Rollback
 * Reverts all selectors back to class-based selectors if needed
 * Use this only if the compatibility fix doesn't work
 */

window.BudgetEmergencyRollback = {
    /**
     * Initialize emergency rollback
     */
    init: function() {
        console.log('🚨 EMERGENCY ROLLBACK ACTIVATED');
        this.rollbackRecalculate();
        this.rollbackTransfer();
        this.rollbackEdit();
        this.rollbackUtilities();
        console.log('🚨 Emergency rollback complete - all selectors reverted to class-based');
    },

    /**
     * Rollback BudgetRecalculate to use only class selectors
     */
    rollbackRecalculate: function() {
        if (window.BudgetRecalculate) {
            // Override the problematic methods
            window.BudgetRecalculate.collectUpdatedRows = function() {
                const updatedRows = [];

                $('.modified-row').each((index, element) => {
                    const row = $(element);
                    const rowId = row.data('id');

                    updatedRows.push({
                        id: rowId,
                        weekly_budget: this.safeParseNumber(row.find('.weekly-budget-cell').text()),
                        weekly_allowance: this.safeParseNumber(row.find('.weekly-allowance-cell').text()),
                        special_allowance: this.safeParseNumber(row.find('.special-allowance-cell').text()),
                        total_weekly_allocation: this.safeParseNumber(row.find('.total-allocation-cell').text()),
                        total_weekly_utilisation: this.safeParseNumber(row.find('.total-utilisation-cell').text()),
                        percent_utilisation: this.safeParseNumber(row.find('.percent-utilisation-cell').text()),
                        weekly_unutilised_fund: this.safeParseNumber(row.find('.weekly-balance-cell').text()),
                        internal_transfers: this.safeParseNumber(row.find('.internal-transfers-cell').text()),
                        notes: row.find('.notes-cell').text().trim()
                    });
                });

                return updatedRows;
            };

            window.BudgetRecalculate.updateRowCells = function(row, calculations) {
                row.find('.total-allocation-cell').text('£' + window.BudgetUtils.formatNumber(calculations.totalAllocation));
                row.find('.percent-utilisation-cell').text(window.BudgetUtils.formatNumber(calculations.percentUtilisation) + '%');
                row.find('.weekly-balance-cell').text('£' + window.BudgetUtils.formatNumber(calculations.weeklyBalance));
                row.find('.balance-fund-cell').text('£' + window.BudgetUtils.formatNumber(calculations.balanceFund));
            };

            window.BudgetRecalculate.recalculateRow = function(row) {
                const rowId = row.data('id');

                // Get current values with safe parsing
                const weeklyBudget = this.safeParseNumber(row.find('.weekly-budget-cell').text());
                const weeklyAllowance = this.safeParseNumber(row.find('.weekly-allowance-cell').text());
                const specialAllowance = this.safeParseNumber(row.find('.special-allowance-cell').text());
                const totalUtilisation = this.safeParseNumber(row.find('.total-utilisation-cell').text());
                const internalTransfers = this.safeParseNumber(row.find('.internal-transfers-cell').text());
                const cumulativeBalance = this.safeParseNumber(row.find('.cumulative-balance-cell').text());

                // Calculate new values
                const calculations = this.performCalculations({
                    weeklyBudget,
                    weeklyAllowance,
                    specialAllowance,
                    totalUtilisation,
                    cumulativeBalance,
                    internalTransfers
                });

                // Update cells with new values
                this.updateRowCells(row, calculations);

                // Mark row as modified
                row.addClass('modified-row');
                this.modifiedRows.add(rowId);
            };

            // Fix event handlers
            window.BudgetRecalculate.bindEvents = function() {
                $('#selectAllCheckbox').on('change', this.handleSelectAll.bind(this));
                $(document).on('change', '.row-checkbox', this.handleRowCheckbox.bind(this));
                $('#recalculateBtn').on('click', this.handleRecalculate.bind(this));
                $('#saveChangesBtn').on('click', this.handleSaveChanges.bind(this));
                $('#cancelChangesBtn').on('click', this.handleCancelChanges.bind(this));
            };

            window.BudgetRecalculate.handleSelectAll = function() {
                const isChecked = $('#selectAllCheckbox').is(':checked');
                $('.row-checkbox').prop('checked', isChecked);
            };

            window.BudgetRecalculate.handleRowCheckbox = function() {
                const allChecked = $('.row-checkbox:checked').length === $('.row-checkbox').length;
                $('#selectAllCheckbox').prop('checked', allChecked);
            };

            window.BudgetRecalculate.handleRecalculate = function() {
                const selectedRows = $('.row-checkbox:checked').closest('tr');

                if (selectedRows.length === 0) {
                    window.BudgetUtils.showRecalculateAlert('Please select at least one row to recalculate.');
                    return;
                }

                this.modifiedRows.clear();
                $('.budget-row').removeClass('modified-row');

                // Sort selected rows by week number to ensure proper order
                const sortedRows = selectedRows.toArray().sort((a, b) => {
                    const weekA = this.getWeekNumber($(a));
                    const weekB = this.getWeekNumber($(b));
                    return weekA - weekB;
                });

                // Recalculate each row in order
                sortedRows.forEach((element) => {
                    this.recalculateRow($(element));
                });

                this.showSaveActions();
            };

            // Re-bind events
            window.BudgetRecalculate.bindEvents();
        }
    },

    /**
     * Rollback BudgetTransfer to use only class selectors
     */
    rollbackTransfer: function() {
        if (window.BudgetTransfer) {
            window.BudgetTransfer.bindEvents = function() {
                console.log('Binding transfer event handlers...');

                // Handle transfer button clicks
                $(document).on('click', '.transfer-btn', this.openTransferModal.bind(this));

                // Handle transfer history button clicks
                $(document).on('click', '.transfer-history-btn', this.openTransferHistoryModal.bind(this));

                // Handle transfer type change
                $(document).on('change', 'input[name="transferType"]', this.handleTransferTypeChange.bind(this));

                // Handle transfer form submission
                $(document).on('click', '.execute-transfer-btn', this.executeTransfer.bind(this));

                // Handle modal close cleanup
                $('#transferModal').on('hidden.bs.modal', this.cleanupModal.bind(this));
                $('#transferHistoryModal').on('hidden.bs.modal', this.cleanupHistoryModal.bind(this));

                // Handle close button clicks (fallback)
                $(document).on('click', '#transferModal .close, #closeTransferModal', this.handleModalClose.bind(this));
                $(document).on('click', '#doneTransferModal', this.handleDoneTransfers.bind(this));
                $(document).on('click', '#transferHistoryModal .close, #transferHistoryModal [data-dismiss="modal"]', this.handleHistoryModalClose.bind(this));

                console.log('Transfer event handlers bound successfully');
            };

            // Re-bind events
            window.BudgetTransfer.bindEvents();
        }
    },

    /**
     * Rollback BudgetEdit to use only class selectors
     */
    rollbackEdit: function() {
        if (window.BudgetEdit) {
            window.BudgetEdit.bindEvents = function() {
                $(document).on('click', '.edit-row-btn', this.handleEditRow.bind(this));
                $(document).on('click', '.save-row-btn', this.handleSaveRow.bind(this));
                $(document).on('click', '.cancel-row-btn', this.handleCancelRow.bind(this));
            };

            // Re-bind events
            window.BudgetEdit.bindEvents();
        }
    },

    /**
     * Rollback BudgetUtilities to use only class selectors
     */
    rollbackUtilities: function() {
        if (window.BudgetUtils && window.BudgetUtils.updateTableCumulativeBalances) {
            // Restore original updateTableCumulativeBalances if it was modified
            // This function should work with class selectors by default
        }
    }
};

// Add emergency rollback button to page
$(document).ready(function() {
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
        $('body').append(`
            <div style="position: fixed; top: 50px; right: 10px; z-index: 10001; background: red; color: white; padding: 10px; border-radius: 5px;">
                <button onclick="BudgetEmergencyRollback.init()" style="background: darkred; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                    🚨 EMERGENCY ROLLBACK
                </button>
                <div style="font-size: 10px; margin-top: 5px;">Click if recalculation is broken</div>
            </div>
        `);
    }
});
