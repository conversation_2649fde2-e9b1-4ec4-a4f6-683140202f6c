<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Budget Summary Card - Test Preview</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    
    <!-- Our Custom CSS -->
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .Container_Fluid_Main {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .marginCenter {
            margin: 0 auto;
        }
        
        .GpBoxAuto {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .shadowWidget {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .BudTitle {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        /* Include our budget summary card styles */
        
        /* ================================================================
           BUDGET SUMMARY CARD STYLES - Futuristic Design
           ================================================================ */

        #budgetSummaryCard {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            border: none !important;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
            overflow: hidden;
            position: relative;
        }

        #budgetSummaryCard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .budget-summary-header {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 20px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .budget-summary-header .BudTitle {
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .summary-icon {
            font-size: 28px;
            color: #ffd700;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
            to { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
        }

        .financial-year-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-left: auto;
            box-shadow: 0 4px 15px rgba(238, 90, 82, 0.4);
        }

        .budget-summary-content {
            padding: 30px 25px;
            position: relative;
        }

        .summary-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 25px;
            height: 100%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .summary-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .summary-section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .section-icon {
            font-size: 20px;
            color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: 0.5px;
        }

        /* Annual Budget Section Styles */
        .annual-budget-section .summary-value-container {
            text-align: center;
            margin: 20px 0;
        }

        .summary-main-value {
            font-size: 36px;
            font-weight: 700;
            color: #27ae60;
            text-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
            line-height: 1.2;
            margin-bottom: 8px;
        }

        .summary-sub-label {
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .summary-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #ddd, transparent);
            margin: 20px 0;
        }

        .summary-details .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .summary-details .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 600;
        }

        /* Generation Configuration Section */
        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr 1fr;
            gap: 12px;
        }

        .config-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .config-item:hover {
            background: #e3f2fd;
            border-color: #667eea;
            transform: scale(1.02);
        }

        .config-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 11px;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .config-icon {
            font-size: 12px;
            color: #667eea;
        }

        .config-value {
            font-size: 16px;
            font-weight: 700;
            color: #2c3e50;
        }

        /* Weekly Breakdown Section */
        .breakdown-grid {
            margin: 20px 0;
        }

        .breakdown-item {
            padding: 12px 0;
            text-align: center;
        }

        .breakdown-label {
            font-size: 13px;
            color: #7f8c8d;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .breakdown-value {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .breakdown-value.highlighted {
            color: #e74c3c;
            font-size: 22px;
            font-weight: 700;
        }

        .breakdown-divider-mini {
            height: 1px;
            background: #ecf0f1;
            margin: 8px 0;
        }

        .utilization-status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .utilization-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .utilization-indicator.status-good {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4);
        }

        .utilization-indicator.status-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        .utilization-indicator.status-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
        }

        .utilization-icon {
            font-size: 16px;
            animation: rotate-slow 4s linear infinite;
        }

        @keyframes rotate-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive Design for Summary Card */
        @media (max-width: 992px) {
            .budget-summary-content .row {
                margin: 0;
            }
            
            .budget-summary-content .col-md-4 {
                margin-bottom: 20px;
                padding: 0 10px;
            }
            
            .summary-main-value {
                font-size: 28px;
            }
            
            .config-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .breakdown-value {
                font-size: 18px;
            }
            
            .breakdown-value.highlighted {
                font-size: 20px;
            }
        }

        @media (max-width: 768px) {
            .budget-summary-header {
                padding: 15px 20px;
            }
            
            .budget-summary-header .BudTitle {
                font-size: 20px;
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }
            
            .financial-year-badge {
                margin-left: 0;
            }
            
            .budget-summary-content {
                padding: 20px 15px;
            }
            
            .summary-section {
                padding: 20px;
                margin-bottom: 15px;
            }
            
            .summary-main-value {
                font-size: 24px;
            }
            
            .config-item {
                padding: 12px;
            }
            
            .config-value {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="Container_Fluid_Main">
        <h1 style="text-align: center; margin-bottom: 30px; color: #333;">
            Budget Summary Card - Preview Demo
        </h1>
        
        <!-- Budget Summary Card Preview -->
        <div class="marginCenter GpBoxAuto shadowWidget mt-4" id="budgetSummaryCard">
            <div class="budget-summary-header">
                <h5 class="BudTitle mb-0">
                    <i class="fa fa-chart-line summary-icon"></i>
                    Budget Allocation Summary
                    <span class="financial-year-badge">2024-2025</span>
                </h5>
            </div>
            
            <div class="budget-summary-content">
                <div class="row">
                    <!-- Annual Budget Overview -->
                    <div class="col-md-4">
                        <div class="summary-section annual-budget-section">
                            <div class="summary-section-header">
                                <i class="fa fa-coins section-icon"></i>
                                <span class="section-title">Annual Budget</span>
                            </div>
                            <div class="summary-value-container">
                                <div class="summary-main-value">
                                    £1,250,000.00
                                </div>
                                <div class="summary-sub-label">Total Allocated</div>
                            </div>
                            <div class="summary-divider"></div>
                                <div class="summary-details">
                                    <div class="detail-row">
                                        <span class="detail-label">Client:</span>
                                        <span class="detail-value">Amica Care Trust</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Unit:</span>
                                        <span class="detail-value">Orchards NH</span>
                                    </div>
                                </div>
                        </div>
                    </div>

                    <!-- Generation Configuration -->
                    <div class="col-md-4">
                        <div class="summary-section generation-config-section">
                            <div class="summary-section-header">
                                <i class="fa fa-cogs section-icon"></i>
                                <span class="section-title">Generation Details</span>
                            </div>
                            <div class="config-grid">
                                                    <div class="config-item">
                                                        <div class="config-label">
                                                            <i class="fa fa-calendar-week config-icon"></i>
                                                            Total Weeks
                                                        </div>
                                                        <div class="config-value">52</div>
                                                    </div>
                                                    <div class="config-item">
                                                        <div class="config-label">
                                                            <i class="fa fa-clock config-icon"></i>
                                                            Week Interval
                                                        </div>
                                                        <div class="config-value">7 days</div>
                                                    </div>
                                                    <div class="config-item">
                                                        <div class="config-label">
                                                            <i class="fa fa-calendar-check config-icon"></i>
                                                            First Week End
                                                        </div>
                                                        <div class="config-value">04/07/2024</div>
                                                    </div>
                                                    <div class="config-item">
                                                        <div class="config-label">
                                                            <i class="fa fa-calendar-alt config-icon"></i>
                                                            Period End
                                                        </div>
                                                        <div class="config-value">03/31/2025</div>
                                                    </div>
                                                    <div class="config-item">
                                                        <div class="config-label">
                                                            <i class="fa fa-percentage config-icon"></i>
                                                            Allowance %
                                                        </div>
                                                        <div class="config-value">15.50%</div>
                                                    </div>
                            </div>
                        </div>
                    </div>

                    <!-- Weekly Budget Breakdown -->
                    <div class="col-md-4">
                        <div class="summary-section weekly-breakdown-section">
                            <div class="summary-section-header">
                                <i class="fa fa-calculator section-icon"></i>
                                <span class="section-title">Weekly Breakdown</span>
                            </div>
                            <div class="breakdown-grid">
                                <div class="breakdown-item">
                                    <div class="breakdown-label">Average Weekly Budget</div>
                                    <div class="breakdown-value">£24,038.46</div>
                                </div>
                                <div class="breakdown-divider-mini"></div>
                                <div class="breakdown-item">
                                    <div class="breakdown-label">Average Weekly Allowance</div>
                                    <div class="breakdown-value">£3,725.96</div>
                                </div>
                                <div class="breakdown-divider-mini"></div>
                                <div class="breakdown-item">
                                    <div class="breakdown-label">Total Weekly Allocation</div>
                                    <div class="breakdown-value highlighted">£1,443,750.00</div>
                                </div>
                                <div class="breakdown-divider-mini"></div>
                                <div class="breakdown-item">
                                    <div class="breakdown-label">Total Weekly Utilisation</div>
                                    <div class="breakdown-value">£856,250.00</div>
                                </div>
                                <div class="breakdown-divider-mini"></div>
                                <div class="breakdown-item">
                                    <div class="breakdown-label">Weekly Budget Balance</div>
                                    <div class="breakdown-value">£587,500.00</div>
                                </div>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="utilization-status">
                                <div class="utilization-indicator status-good">
                                    <i class="fa fa-chart-pie utilization-icon"></i>
                                    <span class="utilization-text">68.5% Utilized</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Demo Info -->
        <div class="marginCenter GpBoxAuto shadowWidget mt-4" style="background: #e8f5e8; border-left: 4px solid #27ae60;">
            <h5 style="color: #27ae60; margin-bottom: 15px;">
                <i class="fa fa-info-circle"></i> Demo Information
            </h5>
            <p><strong>Features Demonstrated:</strong></p>
            <ul>
                <li>🎨 <strong>Futuristic gradient design</strong> with glassmorphism effects</li>
                <li>📊 <strong>Three-section layout</strong>: Annual Budget, Generation Details, Weekly Breakdown</li>
                <li>💰 <strong>Proper currency formatting</strong> with thousand separators</li>
                <li>📱 <strong>Fully responsive design</strong> that adapts to different screen sizes</li>
                <li>✨ <strong>Interactive hover effects</strong> and smooth animations</li>
                <li>🎯 <strong>Status indicators</strong> with color-coded utilization levels</li>
                <li>🔄 <strong>Animated icons</strong> and glowing effects</li>
                <li>📈 <strong>Real-time calculation display</strong> with visual hierarchy</li>
            </ul>
            
            <p><strong>Integration Details:</strong></p>
            <ul>
                <li>This card will appear <strong>after budget generation</strong> when weekly budgets exist</li>
                <li>Data is dynamically populated from the <code>$annualBudget</code> and <code>$weeklyBudgets</code> collections</li>
                <li>Includes JavaScript enhancements for count-up animations and interactive features</li>
                <li>Maintains consistency with existing Smartseek dashboard UI design language</li>
            </ul>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Demo animation script -->
    <script>
        $(document).ready(function() {
            // Add entrance animation to the summary card
            $('#budgetSummaryCard').css({
                'opacity': '0',
                'transform': 'translateY(30px)'
            });
            
            setTimeout(() => {
                $('#budgetSummaryCard').animate({
                    'opacity': '1'
                }, 800);
                
                $('#budgetSummaryCard').css({
                    'transform': 'translateY(0)',
                    'transition': 'transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                });
            }, 300);
            
            // Stagger animation for sections
            $('.summary-section').each(function(index) {
                const section = $(this);
                section.css({
                    'opacity': '0',
                    'transform': 'translateY(20px)'
                });

                setTimeout(() => {
                    section.animate({
                        'opacity': '1'
                    }, 600);
                    
                    section.css({
                        'transform': 'translateY(0)',
                        'transition': 'all 0.6s ease'
                    });
                }, 600 + (index * 200));
            });
        });
    </script>
</body>
</html>
