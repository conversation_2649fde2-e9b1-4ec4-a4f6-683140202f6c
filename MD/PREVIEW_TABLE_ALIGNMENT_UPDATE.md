# Preview Generated Weeks Table Alignment Update

## Overview
Updated the 'Preview Generated Weeks' DataTable to match the layout and structure of the 'Weekly Budget Allocations - Financial Year' table for consistency.

## Changes Made

### 1. Blade Template Updates (`resources/views/clientPortal/budgetForm.blade.php`)

**Updated Headers** to match the main weekly budget table:
- Week Number → Week 
- Week Ending → Week<br> Ending
- Weekly Budget → Weekly<br> Budget  
- Weekly Allowance → Weekly<br> Allowance
- Special Allowance → Special<br> Allowance
- Total Allowance for the Week → Total Allowance<br>for the week
- Total Weekly Utilisation → Total Weekly<br>Utilisation
- Weekly Budget Balance → Weekly<br>Budget Balance
- Weekly Budget Utilisation % → Weekly Budget<br>Utilisation %
- Weekly Balance → Weekly<br> Balance
- % of Utilisation → % of <br>Utilisation
- Consolidated Unutilised Fund → Consolidated<br>Unutilised Fund

**Removed Columns:**
- Internal Transfers (not needed in preview)
- Balance Fund (calculated by backend)
- Notes (as per requirement)

### 2. JavaScript Updates (`public/js/budget-auto-generate.js`)

**Updated `createPreviewRow()` function:**
- Removed Internal Transfers column display
- Removed Balance Fund column display  
- Removed Notes column display
- Reordered columns to match main table structure
- Maintained proper currency formatting and percentage formatting

**Updated `extractWeeksFromPreview()` function:**
- Adjusted cell index mappings to match new column order
- Set default values for Internal Transfers (0), Balance Fund (0), and Notes ('')
- Maintained all required database field mappings

### 3. CSS Updates (`public/css/budget-form.css`)

**Added styling for consistency:**
- Header alignment using `data-column` attributes
- Right-alignment for numerical columns
- Center alignment for Week Ending column
- Bold formatting for Week Number column
- Responsive styling to match main table

## Database Field Mappings

The preview table now aligns with these `ng_client_unit_weekly_budgets` table fields:

| Preview Column | DB Field | Data Type |
|---|---|---|
| Week | week_number | int |
| Week Ending | week_end_date | date |
| Weekly Budget | weekly_budget | decimal(12,2) |
| Weekly Allowance | weekly_allowance | decimal(12,2) |
| Special Allowance | special_allowance | decimal(12,2) |
| Total Allowance for the week | total_weekly_allocation | decimal(12,2) |
| Total Weekly Utilisation | total_weekly_utilisation | decimal(12,2) |
| Weekly Budget Balance | weekly_budget_balance | decimal(12,2) |
| Weekly Budget Utilisation % | weekly_budget_utilisation_percentage | decimal(5,2) |
| Weekly Balance | weekly_unutilised_fund | decimal(12,2) |
| % of Utilisation | percent_utilisation | decimal(5,2) |
| Consolidated Unutilised Fund | consolidated_unutilised_fund | decimal(12,2) |

## Features Maintained

- ✅ Currency formatting (£ symbol with comma separators)
- ✅ Percentage formatting (% symbol)
- ✅ Right-alignment for numerical values
- ✅ Center-alignment for week numbers and dates
- ✅ Responsive design support
- ✅ Consistent column widths and header styling
- ✅ Data extraction for saving generated weeks
- ✅ Calculation accuracy for all derived fields

## Testing

1. Navigate to Budget Form page
2. Enter Annual Budget amount
3. Click "Preview Generated Weeks"
4. Verify column headers match main table layout
5. Verify data formatting (currency, percentages)
6. Verify column alignment (center, right)
7. Test responsiveness on different screen sizes
8. Test Save functionality to ensure data mapping works correctly

## Notes

- Internal Transfers column not shown in preview as it's set to 0 for new generations
- Balance Fund calculated by backend after saving
- Notes field excluded from preview as per requirements
- All styling matches the main Weekly Budget Allocations table for visual consistency
