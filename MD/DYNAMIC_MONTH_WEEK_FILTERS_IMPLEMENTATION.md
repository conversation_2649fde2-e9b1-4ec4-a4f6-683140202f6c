# Dynamic Month & Week Dropdowns Implementation

## Overview
This document describes the implementation of dynamic month and week dropdowns for the dashboard, along with the updated Balance card calculation logic using the `client_unit_weekly_budgets` table.

## Features Implemented

### 1. Dynamic Month & Week Dropdowns

#### Data Source
- **Table**: `client_unit_weekly_budgets` (Laravel model: `ClientUnitWeeklyBudget`)
- **Month Population**: Based on distinct `week_end_date` entries in the table
- **Week Population**: Based on actual available `week_number` entries

#### Filtering Logic
- Month dropdown is enabled only after unit and financial year are selected
- Week dropdown is enabled only after unit and financial year are selected
- Week dropdown filters based on selected month (optional)
- All dropdowns cascade properly: Unit → Financial Year → Month → Week

#### Month Format
- Extracted from `week_end_date` using Carbon
- Displayed as readable labels (e.g., "January", "February")
- Months are ordered chronologically

#### Week Format
- Uses actual `week_number` from the database
- Displayed as "Week X (Start Date - End Date)" format
- Example: "Week 12 (Mar 15 - Mar 21, 2025)"

### 2. Updated Balance Card Calculation

#### New Formula
```
Balance = weekly_budget - total_weekly_utilisation
```

#### Old vs New Logic
- **Old Calculation**: Used static `balance_fund` value
- **New Calculation**: Dynamic calculation using `weekly_budget - total_weekly_utilisation`

#### Aggregation Rules
- **Single Week Selected**: Balance for that specific week
- **Month Selected**: `SUM(weekly_budget) - SUM(total_weekly_utilisation)` for all weeks in that month
- **No Specific Period**: Total across all available weeks

#### Visual Indicators
- **Positive Balance**: Green arrow (↑) 
- **Negative Balance**: Red arrow (↓)
- **Context Label**: Shows "(Week X)", "(Month Name)", or "(Total)" based on selection

## File Changes Made

### 1. Backend Changes

#### `app/Http/Controllers/ClientPortal/DashboardController.php`
- **New Methods**:
  - `getMonthsForUnit()` - AJAX endpoint for month dropdown
  - `getWeeksForUnit()` - AJAX endpoint for week dropdown
  - `getMonthsForUnitAndYear()` - Helper to get available months
  - `getWeeksForUnitYearAndMonth()` - Helper to get available weeks

- **Updated Methods**:
  - `index()` - Now accepts month/week parameters
  - `getFilteredData()` - Supports month/week filtering
  - `getDashboardData()` - Updated to handle month/week filters
  - `calculateBalanceData()` - **NEW BALANCE FORMULA IMPLEMENTED**
  - `calculateBudgetData()` - Supports week/month specific data
  - `calculateExpenseData()` - Supports week/month filtering
  - `calculateBookingReasons()` - Supports week/month filtering
  - `getBudgetByUnit()` - Supports week/month specific budgets

#### `routes/web.php`
- Added new routes:
  - `GET /dashboard/months` → `DashboardController@getMonthsForUnit`
  - `GET /dashboard/weeks` → `DashboardController@getWeeksForUnit`

### 2. Frontend Changes

#### `resources/views/common/dashboard/dashboard_wrapper.blade.php`
- Updated month dropdown with dynamic population
- Updated week dropdown with dynamic population  
- Added proper disabled states and selections
- Connected to new filter variables

#### `resources/views/common/dashboard/component/card/header_cards.blade.php`
- Updated Balance card to show new calculation context
- Added dynamic labels showing selected period
- Improved visual indicators for balance trends

#### `public/js/dashboard-filters.js`
- **Complete Rewrite** to support 4-level cascading dropdowns
- **New Methods**:
  - `loadMonths()` - Load months via AJAX
  - `loadWeeks()` - Load weeks via AJAX
  - `populateMonths()` - Populate month dropdown
  - `populateWeeks()` - Populate week dropdown
  - `resetMonthDropdown()` - Reset month to initial state
  - `resetWeekDropdown()` - Reset week to initial state
  - `handleMonthChange()` - Handle month selection
  - `handleWeekChange()` - Handle week selection

- **Updated Methods**:
  - `getCurrentFilters()` - Now includes month/week
  - `refreshPageWithFilters()` - Includes month/week in URL
  - `refreshDashboardData()` - Sends month/week in AJAX

## API Endpoints

### Get Months for Unit
```
GET /dashboard/months?unit_id={id}&financial_year={year}
```
**Response**:
```json
{
  "months": [
    {"value": 1, "label": "January"},
    {"value": 2, "label": "February"}
  ]
}
```

### Get Weeks for Unit
```
GET /dashboard/weeks?unit_id={id}&financial_year={year}&month={month}
```
**Response**:
```json
{
  "weeks": [
    {
      "id": 123,
      "week_number": 12,
      "label": "Week 12 (Mar 15 - Mar 21, 2025)",
      "week_start_date": "2025-03-15",
      "week_end_date": "2025-03-21"
    }
  ]
}
```

### Get Filtered Data
```
GET /dashboard/filtered-data?unit_id={id}&financial_year={year}&month={month}&week={week}
```

## Usage Examples

### 1. Filter by Specific Week
1. Select Unit: "Care Home A"
2. Select Financial Year: "2025-26"
3. Select Month: "March" 
4. Select Week: "Week 12 (Mar 15 - Mar 21, 2025)"

**Result**: Balance card shows balance for that specific week using `weekly_budget - total_weekly_utilisation`

### 2. Filter by Month
1. Select Unit: "Care Home A"
2. Select Financial Year: "2025-26"
3. Select Month: "March"
4. Leave Week: "Select Week"

**Result**: Balance card shows aggregated balance for all weeks in March

### 3. Filter by Financial Year Only
1. Select Unit: "Care Home A"
2. Select Financial Year: "2025-26"
3. Leave Month and Week: "Select..."

**Result**: Balance card shows total balance for entire financial year

## Database Tables Used

### Primary Table: `client_unit_weekly_budgets`
**Key Fields**:
- `week_number` - Used for week dropdown
- `week_start_date` / `week_end_date` - Used for month extraction and week labels
- `weekly_budget` - Used in new balance calculation
- `total_weekly_utilisation` - Used in new balance calculation
- `client_unit_annual_budget_id` - Links to annual budget

### Related Tables:
- `client_unit_annual_budgets` - For financial year filtering
- `client_units` - For unit information

## Fallback Behavior

### No Data Available
- Dropdowns show "Select Unit and Financial Year" message
- Balance card shows £0.00
- No errors thrown, graceful degradation

### Unauthorized Access
- 403 error returned for units user doesn't have access to
- Proper authentication checks maintained

### Network Errors
- Dropdowns show "Error loading data" message
- JavaScript handles errors gracefully
- Console logging for debugging

## Security Features

### Authentication
- All endpoints require authenticated user
- `userHasAccessToUnit()` checks prevent unauthorized access

### Validation
- Server-side validation for all parameters
- Exists checks for unit IDs
- Proper data type validation

### CSRF Protection
- All AJAX requests include proper headers
- Laravel CSRF middleware protection maintained

## Performance Considerations

### Database Optimization
- Efficient queries using indexed fields
- Limited data selection (only required fields)
- Proper eager loading where applicable

### Frontend Optimization
- Cascading loads prevent unnecessary requests
- Loading states provide user feedback
- Error handling prevents broken states

### Caching Opportunities
- Month/week data could be cached per unit/year
- Consider implementing browser caching for static dropdown data

## Testing Recommendations

### Manual Testing
1. Test all 4 dropdown combinations
2. Verify balance calculations are correct
3. Test with different units and financial years
4. Verify unauthorized access is blocked
5. Test network error scenarios

### Database Testing
```sql
-- Verify balance calculation
SELECT 
    week_number,
    weekly_budget,
    total_weekly_utilisation,
    (weekly_budget - total_weekly_utilisation) as calculated_balance
FROM client_unit_weekly_budgets 
WHERE client_unit_annual_budget_id = {budget_id}
ORDER BY week_number;
```

### Browser Testing
- Test in Chrome, Firefox, Safari
- Verify mobile responsiveness
- Test JavaScript error handling
- Verify loading states work properly

## Future Enhancements

### Potential Improvements
1. **Caching**: Implement Redis caching for dropdown data
2. **Real-time Updates**: WebSocket updates when data changes
3. **Bulk Operations**: Select multiple weeks/months
4. **Export**: Export filtered data to CSV/PDF
5. **Saved Filters**: Allow users to save favorite filter combinations
6. **Advanced Filters**: Date range picker instead of dropdowns

### Performance Optimization
1. **Lazy Loading**: Load dropdowns only when needed
2. **Debounced Requests**: Prevent rapid-fire AJAX calls
3. **Pagination**: For units with many weeks
4. **Compression**: Gzip API responses

## Troubleshooting

### Common Issues

#### Dropdowns Not Loading
- Check network tab for failed AJAX requests
- Verify routes are registered: `docker exec -it clientportal-php php artisan route:list | grep dashboard`
- Check PHP error logs for server-side issues

#### Balance Calculation Wrong
- Verify `weekly_budget` and `total_weekly_utilisation` data in database
- Check filtering logic in `calculateBalanceData()` method
- Ensure proper aggregation for multiple weeks

#### JavaScript Errors
- Check browser console for JavaScript errors
- Verify `dashboard-filters.js` is loaded properly
- Check for conflicting JavaScript libraries

#### Authentication Issues
- Verify user is logged in and has proper permissions
- Check `userHasAccessToUnit()` logic for specific scenarios
- Verify session is not expired

### Debug Commands
```bash
# Check routes
docker exec -it clientportal-php php artisan route:list | grep dashboard

# Check PHP syntax
docker exec -it clientportal-php php -l app/Http/Controllers/ClientPortal/DashboardController.php

# Clear Laravel cache
docker exec -it clientportal-php php artisan cache:clear
docker exec -it clientportal-php php artisan config:clear
docker exec -it clientportal-php php artisan view:clear
```

---

## Summary

The implementation successfully provides:

✅ **Dynamic Month Dropdowns** - Populated from `client_unit_weekly_budgets.week_end_date`  
✅ **Dynamic Week Dropdowns** - Populated from actual `week_number` entries  
✅ **Cascading Filter Logic** - Unit → Financial Year → Month → Week  
✅ **New Balance Calculation** - `weekly_budget - total_weekly_utilisation`  
✅ **Proper Aggregation** - Sum for multiple weeks/months  
✅ **Visual Indicators** - Up/down arrows based on balance  
✅ **Fallback Support** - "Select Unit and Financial Year" when no data  
✅ **Security** - Authentication and authorization checks  
✅ **Error Handling** - Graceful degradation on failures  

The dashboard now provides granular filtering capabilities with accurate balance calculations based on actual weekly budget data.
